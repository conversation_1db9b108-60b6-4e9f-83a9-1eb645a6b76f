import React from 'react';

import { ContactInfoItem as SharedContactInfoItem, type BaseItemProps } from './ItemRenderer';
import { type AlignmentVariant, type SizeVariant, type ColorVariant } from '@/lib/style-utilities';

interface ContactInfoItemProps {
  icon: React.ReactNode;
  title: string;
  content: string | React.ReactNode;
  layout?: 'horizontal' | 'vertical';
  iconVariant?: ColorVariant;
  iconSize?: SizeVariant;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
}

const ContactInfoItem: React.FC<ContactInfoItemProps> = (props) => {
  return <SharedContactInfoItem {...props} />;
};

export default ContactInfoItem;
