import React, { createContext, useContext, ReactNode } from 'react';

import ErrorBoundary from '@/components/ErrorBoundary';

/**
 * Base context utility for creating type-safe contexts with error boundaries
 */
export function createSafeContext<T>(
  contextName: string,
  defaultValue?: T
) {
  const Context = createContext<T | undefined>(defaultValue);

  function useContextSafe(): T {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(`use${contextName} must be used within a ${contextName}Provider`);
    }
    return context;
  }

  function Provider({ 
    children, 
    value,
    errorFallback 
  }: { 
    children: ReactNode; 
    value: T;
    errorFallback?: ReactNode;
  }) {
    return (
      <ErrorBoundary fallback={errorFallback}>
        <Context.Provider value={value}>
          {children}
        </Context.Provider>
      </ErrorBoundary>
    );
  }

  return {
    Context,
    Provider,
    useContext: useContextSafe
  };
}

/**
 * Base provider props interface
 */
export interface BaseProviderProps {
  children: ReactNode;
  errorFallback?: ReactNode;
}

/**
 * Common context patterns
 */
export const contextPatterns = {
  /**
   * Create a loading state pattern
   */
  createLoadingState: <T>(initialValue: T) => ({
    data: initialValue,
    isLoaded: false,
    isLoading: false,
    error: null as Error | null
  }),

  /**
   * Create a validation pattern
   */
  createValidationPattern: <T>(
    data: T,
    validator: (data: T) => boolean,
    fallback: T
  ): T => {
    try {
      if (validator(data)) {
        return data;
      }
      return fallback;
    } catch {
      return fallback;
    }
  },

  /**
   * Create a memoized value pattern
   */
  createMemoizedValue: <T>(
    factory: () => T,
    dependencies: React.DependencyList
  ) => React.useMemo(factory, dependencies),

  /**
   * Create an effect cleanup pattern
   */
  createEffectCleanup: (
    effect: () => (() => void) | void,
    dependencies: React.DependencyList
  ) => React.useEffect(effect, dependencies)
};

export default createSafeContext;
