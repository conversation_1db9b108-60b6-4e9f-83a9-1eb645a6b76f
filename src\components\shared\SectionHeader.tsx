import React from 'react';

import { cn } from '@/lib/utils';
import { getSizeClass, getAlignmentClass, type SizeVariant, type AlignmentVariant } from '@/lib/style-utilities';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  alignment?: AlignmentVariant;
  size?: SizeVariant;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  descriptionClassName?: string;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  description,
  alignment = 'center',
  size = 'md',
  className = '',
  titleClassName = '',
  subtitleClassName = '',
  descriptionClassName = ''
}) => {
  const alignmentClass = getAlignmentClass(alignment || 'center');
  const titleSizeClass = getSizeClass(size || 'md', 'heading');
  const subtitleSizeClass = getSizeClass(size || 'md', 'subtitle');
  const descriptionSizeClass = getSizeClass(size || 'md', 'description');

  return (
    <div className={cn(alignmentClass, className)}>
      <h2 className={cn(
        titleSizeClass,
        'font-bold mb-4',
        titleClassName
      )}>
        {title}
      </h2>

      {subtitle && (
        <h3 className={cn(
          subtitleSizeClass,
          'text-primary mb-4',
          subtitleClassName
        )}>
          {subtitle}
        </h3>
      )}

      {description && (
        <p className={cn(
          descriptionSizeClass,
          'text-muted-foreground',
          alignment === 'center' ? 'max-w-3xl mx-auto' : '',
          descriptionClassName
        )}>
          {description}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
