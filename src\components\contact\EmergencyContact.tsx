import React from 'react';

import { ContactInfoRenderer, type ContactSection } from '@/components/shared/ContactInfoRenderer';

interface EmergencyContactProps {
  title: string;
  sections: ContactSection[];
}

const EmergencyContact: React.FC<EmergencyContactProps> = ({
  title,
  sections
}) => {
  return (
    <ContactInfoRenderer
      title={title}
      sections={sections}
      showTitle={true}
    />
  );
};

export default EmergencyContact;
