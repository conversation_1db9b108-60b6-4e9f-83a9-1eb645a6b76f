import { useCallback, useRef, useState } from 'react';

import { logWarning } from '@/lib/dev-console';

/**
 * Base async state type
 */
export type BaseAsyncState = 'idle' | 'loading' | 'success' | 'error' | 'empty';

/**
 * Base options for async state management
 */
export interface BaseAsyncOptions<T> {
  initialData?: T;
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  onEmpty?: () => void;
}

/**
 * Base return interface for async state hooks
 */
export interface BaseAsyncReturn<T> {
  data: T | null;
  state: BaseAsyncState;
  error: Error | null;
  isLoading: boolean;
  isEmpty: boolean;
  hasError: boolean;
  isSuccess: boolean;
  retry: () => Promise<void>;
  reset: () => void;
}

/**
 * Base hook for async state management
 * Consolidates common patterns between useAsyncData and useContentState
 */
export function useBaseAsyncState<T>(
  options: BaseAsyncOptions<T> = {}
): BaseAsyncReturn<T> & {
  setData: (data: T | null) => void;
  setState: (state: BaseAsyncState) => void;
  setError: (error: Error | null) => void;
  executeWithRetry: (loadFn: () => Promise<T>) => Promise<void>;
} {
  const {
    initialData = null,
    retryAttempts = 3,
    retryDelay = 1000,
    timeout = 30000,
    onSuccess,
    onError,
    onEmpty
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [state, setState] = useState<BaseAsyncState>('idle');
  const [error, setError] = useState<Error | null>(null);
  const [currentLoadFn, setCurrentLoadFn] = useState<(() => Promise<T>) | null>(null);
  
  const retryCountRef = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // Cleanup timeout on unmount
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    isMountedRef.current = false;
  }, []);

  // Execute function with retry logic
  const executeWithRetry = useCallback(async (loadFn: () => Promise<T>) => {
    if (!isMountedRef.current) return;

    setCurrentLoadFn(() => loadFn);
    setState('loading');
    setError(null);
    retryCountRef.current = 0;

    const attemptLoad = async (): Promise<void> => {
      try {
        // Set timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
          timeoutRef.current = setTimeout(() => {
            reject(new Error(`Operation timed out after ${timeout}ms`));
          }, timeout);
        });

        // Execute load function with timeout
        const result = await Promise.race([loadFn(), timeoutPromise]);

        // Clear timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        if (!isMountedRef.current) return;

        // Handle result
        if (result === null || result === undefined) {
          setState('empty');
          setData(null);
          onEmpty?.();
        } else {
          setState('success');
          setData(result);
          onSuccess?.(result);
        }
      } catch (err) {
        if (!isMountedRef.current) return;

        const error = err instanceof Error ? err : new Error(String(err));
        
        // Clear timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Retry logic
        if (retryCountRef.current < retryAttempts) {
          retryCountRef.current++;
          logWarning(`Attempt ${retryCountRef.current} failed, retrying in ${retryDelay}ms...`);
          
          setTimeout(() => {
            if (isMountedRef.current) {
              attemptLoad();
            }
          }, retryDelay);
          return;
        }

        // Final failure
        setState('error');
        setError(error);
        onError?.(error);
      }
    };

    await attemptLoad();
  }, [retryAttempts, retryDelay, timeout, onSuccess, onError, onEmpty]);

  // Retry current operation
  const retry = useCallback(async () => {
    if (currentLoadFn) {
      await executeWithRetry(currentLoadFn);
    }
  }, [currentLoadFn, executeWithRetry]);

  // Reset state
  const reset = useCallback(() => {
    cleanup();
    setData(initialData);
    setState('idle');
    setError(null);
    setCurrentLoadFn(null);
    retryCountRef.current = 0;
  }, [initialData, cleanup]);

  // Computed properties
  const isLoading = state === 'loading';
  const isEmpty = state === 'empty';
  const hasError = state === 'error';
  const isSuccess = state === 'success';

  return {
    data,
    state,
    error,
    isLoading,
    isEmpty,
    hasError,
    isSuccess,
    retry,
    reset,
    setData,
    setState,
    setError,
    executeWithRetry
  };
}

export default useBaseAsyncState;
