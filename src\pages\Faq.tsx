import React, { useEffect, useState, useMemo } from 'react';

import FAQCallToAction from '@/components/faq/FAQCallToAction';
import FAQCategory from '@/components/faq/FAQCategory';
import FAQHero from '@/components/faq/FAQHero';
import FAQSearchBar from '@/components/faq/FAQSearchBar';
import FAQSidebar from '@/components/faq/FAQSidebar';
import Footer from '@/components/Footer';
import Navbar from '@/components/Navbar';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { faqCategories } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';
import en from '@/locales/en';

/**
 * Unified FAQ Component
 *
 * This component integrates all FAQ content from the documentation
 * and uses the modern modular architecture for better maintainability.
 *
 * Features:
 * - Complete FAQ coverage (33 questions across 6 categories)
 * - Modular component architecture
 * - External data structure
 * - Responsive design
 * - Accessibility features
 */

const Faq: React.FC = () => {
  const { t } = useLanguage();
  const deviceInfo = useDeviceDetection();

  // Enhanced state management
  const [activeCategory, setActiveCategory] = useState<number | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedCategory, setHighlightedCategory] = useState<number | undefined>(undefined);

  // Safe fallback for translations
  const safeT = t || en;
  const finalT = (safeT && safeT.navigation && safeT.contact && safeT.cta) ? safeT : {
    navigation: {
      home: "Home",
      expertise: "Expertise",
      about: "About",
      locations: "Locations",
      patientResources: "Patient Resources",
      contact: "Contact",
      bookAppointment: "Book Appointment",
      language: "Language",
      menu: "Menu",
      close: "Close",
      skipToContent: "Skip to Content"
    },
    contact: {
      title: "Contact",
      subtitle: "Get in Touch",
      description: "Contact information",
      phone: "Phone",
      email: "Email",
      address: "Address",
      hours: "Hours",
      form: {
        name: "Name",
        email: "Email",
        phone: "Phone",
        message: "Message",
        submit: "Submit",
        success: "Success",
        error: "Error"
      }
    },
    cta: {
      title: "Ready to Schedule Your Consultation?",
      description: "Take the first step towards better health with expert neurosurgical care",
      primaryButton: "Book Appointment",
      secondaryButton: "Contact Us",
      phone: "Phone",
      email: "Email"
    }
  };

  // Enhanced search functionality
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return faqCategories;

    return faqCategories.map(category => ({
      ...category,
      questions: category.questions.filter(
        faq =>
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(category => category.questions.length > 0);
  }, [searchQuery]);

  // Calculate total questions for hero
  const totalQuestions = faqCategories.reduce((total, cat) => total + cat.questions.length, 0);

  // Enhanced handlers
  const handleCategoryClick = (index: number) => {
    setActiveCategory(index);
    setHighlightedCategory(index);

    // Clear highlight after animation
    setTimeout(() => setHighlightedCategory(undefined), 2000);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setActiveCategory(undefined);
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const heroData = {
    title: "Frequently Asked Questions",
    subtitle: "Find answers to common questions about neurosurgical procedures and treatments",
    description: 'Welcome to our comprehensive FAQ section. Here you\'ll find answers to common questions about neurosurgical procedures, recovery expectations, and specific treatments offered by Dr Ales Aliashkevich. If you don\'t find the information you\'re looking for, please don\'t hesitate to contact us.',
    imageSrc: '/images/Ales-Aliashkevich-consulting-neurosurgery.jpg',
    imageAlt: 'Dr. Ales Aliashkevich consulting with a patient'
  };

  return (
    <div className="min-h-screen flex flex-col bg-slate-50/50 dark:bg-slate-900">
      {/* Skip to main content link */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 transition-all duration-200"
      >
        Skip to main content
      </a>

      <Navbar />

      <main id="main-content" className="flex-1 pt-20">
        <FAQHero
          title={heroData.title}
          subtitle={heroData.subtitle}
          description={heroData.description}
          imageSrc={heroData.imageSrc}
          imageAlt={heroData.imageAlt}
          totalQuestions={totalQuestions}
          categories={faqCategories.length}
        />

        {/* Enhanced FAQ Content */}
        <section className={cn(
          "relative",
          deviceInfo.isMobile ? "py-8" : "py-16"
        )}>
          <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
            {/* Enhanced Search Bar */}
            <div className="mb-12">
              <div className="max-w-2xl mx-auto">
                <FAQSearchBar
                  onSearch={handleSearch}
                  placeholder="Search frequently asked questions..."
                  className="w-full"
                />
              </div>
            </div>

            {/* Search Results Info */}
            {searchQuery && (
              <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    {filteredCategories.length > 0
                      ? `Found ${filteredCategories.reduce((total, cat) => total + cat.questions.length, 0)} results for "${searchQuery}"`
                      : `No results found for "${searchQuery}". Try different keywords.`
                    }
                  </p>
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Clear search
                  </button>
                </div>
              </div>
            )}

            <div className={cn(
              "relative",
              deviceInfo.isMobile
                ? "grid grid-cols-1 gap-8"
                : "grid grid-cols-1 lg:grid-cols-5 gap-12"
            )}>
              <div className={cn(
                deviceInfo.isMobile ? "" : "lg:col-span-2"
              )}>
                <FAQSidebar
                  categories={faqCategories}
                  title="Categories"
                  activeCategory={activeCategory}
                  onCategoryClick={handleCategoryClick}
                  onSearch={handleSearch}
                />
              </div>

              {/* Premium FAQ accordions */}
              <div className={cn(
                "relative",
                deviceInfo.isMobile
                  ? "order-1 space-y-10"
                  : "lg:col-span-3 space-y-16"
              )}>
                {(searchQuery ? filteredCategories : faqCategories).map((category, categoryIndex) => (
                  <FAQCategory
                    key={category.id}
                    category={category}
                    categoryIndex={categoryIndex}
                    isHighlighted={highlightedCategory === categoryIndex}
                  />
                ))}

                {/* No results message */}
                {searchQuery && filteredCategories.length === 0 && (
                  <div className="text-center py-12">
                    <div className="max-w-md mx-auto">
                      <div className="w-16 h-16 mx-auto mb-4 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                        No results found
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        We couldn't find any questions matching your search. Try using different keywords or browse our categories.
                      </p>
                      <button
                        onClick={() => setSearchQuery('')}
                        className="text-primary hover:underline font-medium"
                      >
                        Clear search and view all questions
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        <FAQCallToAction
          title={finalT.cta.title}
          description={finalT.cta.description}
          primaryButtonText={finalT.cta.primaryButton}
          primaryButtonLink="/appointments"
          secondaryButtonText={finalT.navigation.contact}
          secondaryButtonLink="/contact"
        />
      </main>

      <Footer />
    </div>
  );
};

Faq.displayName = 'Faq';

export default Faq;


