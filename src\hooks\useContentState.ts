import { useCallback } from 'react';

import { validateArray, validateString, ValidationResult } from '@/lib/content-validation';
import { useBaseAsyncState, type BaseAsyncOptions, type BaseAsyncState } from './useBaseAsyncState';

export type ContentState = BaseAsyncState;

export interface ContentStateOptions<T> extends BaseAsyncOptions<T> {
  validator?: (data: T) => ValidationResult<T>;
}

export interface UseContentStateReturn<T> {
  data: T | null;
  state: ContentState;
  error: Error | null;
  isLoading: boolean;
  isEmpty: boolean;
  hasError: boolean;
  retry: () => Promise<void>;
  setData: (data: T) => void;
  reset: () => void;
  load: (loadFn: () => Promise<T>) => Promise<void>;
}

/**
 * Hook for managing content state with validation and error handling
 */
export function useContentState<T>(
  options: ContentStateOptions<T> = {}
): UseContentStateReturn<T> {
  const { validator, ...baseOptions } = options;

  const baseState = useBaseAsyncState<T>(baseOptions);

  // Enhanced setData with validation
  const setData = useCallback((newData: T) => {
    if (validator) {
      const validation = validator(newData);
      if (!validation.isValid) {
        baseState.setError(new Error(validation.error || 'Data validation failed'));
        baseState.setState('error');
        return;
      }
      baseState.setData(validation.data);
    } else {
      baseState.setData(newData);
    }

    // Check if data is empty
    if (newData === null || newData === undefined) {
      baseState.setState('empty');
    } else if (Array.isArray(newData) && newData.length === 0) {
      baseState.setState('empty');
    } else {
      baseState.setState('success');
    }
  }, [validator, baseState]);

  // Load data with validation
  const load = useCallback(async (loadFn: () => Promise<T>) => {
    const enhancedLoadFn = async (): Promise<T> => {
      const result = await loadFn();

      if (validator) {
        const validation = validator(result);
        if (!validation.isValid) {
          throw new Error(validation.error || 'Data validation failed');
        }
        return validation.data;
      }

      return result;
    };

    await baseState.executeWithRetry(enhancedLoadFn);
  }, [validator, baseState]);

  return {
    data: baseState.data,
    state: baseState.state,
    error: baseState.error,
    isLoading: baseState.isLoading,
    isEmpty: baseState.isEmpty,
    hasError: baseState.hasError,
    retry: baseState.retry,
    setData,
    reset: baseState.reset,
    load
  };
}

/**
 * Specialized hook for array content
 */
export function useArrayContentState<T>(
  options: ContentStateOptions<T[]> & {
    minLength?: number;
    maxLength?: number;
    itemValidator?: (item: T) => boolean;
  } = {}
): UseContentStateReturn<T[]> {
  const { minLength = 0, maxLength, itemValidator, ...baseOptions } = options;

  const validator = useCallback((data: T[]) => {
    return validateArray(data, {
      minLength,
      maxLength,
      itemValidator,
      fallback: []
    });
  }, [minLength, maxLength, itemValidator]);

  return useContentState<T[]>({
    ...baseOptions,
    validator
  });
}

/**
 * Specialized hook for string content
 */
export function useStringContentState(
  options: ContentStateOptions<string> & {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    allowEmpty?: boolean;
    trim?: boolean;
  } = {}
): UseContentStateReturn<string> {
  const { minLength = 0, maxLength, pattern, allowEmpty = false, trim = true, ...baseOptions } = options;

  const validator = useCallback((data: string) => {
    return validateString(data, {
      minLength,
      maxLength,
      pattern,
      allowEmpty,
      trim,
      fallback: ''
    });
  }, [minLength, maxLength, pattern, allowEmpty, trim]);

  return useContentState<string>({
    ...baseOptions,
    validator
  });
}

/**
 * Hook for managing search results with empty states
 */
export function useSearchContentState<T>(
  searchFn: (query: string) => Promise<T[]>,
  options: ContentStateOptions<T[]> = {}
): UseContentStateReturn<T[]> & {
  search: (query: string) => Promise<void>;
  query: string;
  hasSearched: boolean;
} {
  const [query, setQuery] = useState('');
  const [hasSearched, setHasSearched] = useState(false);

  const contentState = useArrayContentState<T>(options);

  const search = useCallback(async (searchQuery: string) => {
    setQuery(searchQuery);
    setHasSearched(true);

    if (searchQuery.trim().length === 0) {
      contentState.setData([]);
      return;
    }

    await contentState.load(() => searchFn(searchQuery));
  }, [searchFn, contentState]);

  return {
    ...contentState,
    search,
    query,
    hasSearched
  };
}

/**
 * Hook for managing paginated content
 */
export function usePaginatedContentState<T>(
  loadFn: (page: number, limit: number) => Promise<{ items: T[]; total: number; hasMore: boolean }>,
  options: ContentStateOptions<T[]> & {
    initialPage?: number;
    pageSize?: number;
  } = {}
): UseContentStateReturn<T[]> & {
  page: number;
  total: number;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  loadPage: (page: number) => Promise<void>;
  refresh: () => Promise<void>;
} {
  const { initialPage = 1, pageSize = 10, ...baseOptions } = options;

  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [allItems, setAllItems] = useState<T[]>([]);

  const contentState = useArrayContentState<T>(baseOptions);

  const loadPage = useCallback(async (targetPage: number) => {
    setPage(targetPage);

    await contentState.load(async () => {
      const result = await loadFn(targetPage, pageSize);
      setTotal(result.total);
      setHasMore(result.hasMore);

      if (targetPage === 1) {
        setAllItems(result.items);
      } else {
        setAllItems(prev => [...prev, ...result.items]);
      }

      return result.items;
    });
  }, [loadFn, pageSize, contentState]);

  const loadMore = useCallback(async () => {
    if (hasMore && !contentState.isLoading) {
      await loadPage(page + 1);
    }
  }, [hasMore, contentState.isLoading, page, loadPage]);

  const refresh = useCallback(async () => {
    setAllItems([]);
    await loadPage(1);
  }, [loadPage]);

  return {
    ...contentState,
    data: allItems,
    page,
    total,
    hasMore,
    loadMore,
    loadPage,
    refresh
  };
}