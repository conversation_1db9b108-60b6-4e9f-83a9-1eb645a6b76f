import { MapPin, Navigation, Eye, Car, Train, Accessibility, Phone, Calendar } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface MapFeatures {
  interactiveMap: boolean;
  streetView: boolean;
  directions: boolean;
  accessibility: boolean;
}

interface Transportation {
  publicTransport: string[];
  parking: string[];
  accessibility: string[];
}

interface InteractiveMapsSectionProps {
  title: string;
  embedUrl: string;
  features: MapFeatures;
  transportation: Transportation;
}

const InteractiveMapsSection: React.FC<InteractiveMapsSectionProps> = ({
  title,
  embedUrl,
  features,
  transportation
}) => {
  const deviceInfo = useDeviceDetection();

  const mapFeaturesList = [
    {
      icon: MapPin,
      label: "Interactive Map",
      enabled: features.interactiveMap,
      description: "Zoom, pan, and explore the area"
    },
    {
      icon: Eye,
      label: "Street View",
      enabled: features.streetView,
      description: "360° street-level imagery"
    },
    {
      icon: Navigation,
      label: "Directions",
      enabled: features.directions,
      description: "Turn-by-turn navigation"
    },
    {
      icon: Accessibility,
      label: "Accessibility",
      enabled: features.accessibility,
      description: "Accessibility information"
    }
  ];

  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Interactive map with directions, street view, and accessibility information
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 lg:grid-cols-3"
        )}>
          {/* Interactive Map */}
          <div className={cn(
            deviceInfo.isMobile ? "lg:col-span-1" : "lg:col-span-2"
          )}>
            <Card className="shadow-lg h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  Interactive Location Map
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative rounded-lg overflow-hidden">
                  <iframe
                    src={embedUrl}
                    width="100%"
                    height={deviceInfo.isMobile ? "300" : "400"}
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Office Location Map"
                    className="rounded-lg"
                  />
                </div>
                
                {/* Map Controls */}
                <div className="mt-4 flex flex-wrap gap-2">
                  <Button asChild variant="outline" size="sm">
                    <a 
                      href="https://maps.google.com/?q=619+Canterbury+Road+Surrey+Hills+VIC+3127" 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      Open in Google Maps
                    </a>
                  </Button>
                  
                  <Button asChild variant="outline" size="sm">
                    <a 
                      href="https://maps.google.com/maps/dir/?api=1&destination=-37.8204709,145.1015663" 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      <Navigation className="h-4 w-4 mr-2" />
                      Get Directions
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Map Features & Transportation */}
          <div className="space-y-6">
            {/* Map Features */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg">Map Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mapFeaturesList.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className={cn(
                        "p-2 rounded-lg",
                        feature.enabled 
                          ? "bg-green-100 text-green-600" 
                          : "bg-gray-100 text-gray-400"
                      )}>
                        <feature.icon className="h-4 w-4" />
                      </div>
                      <div>
                        <p className={cn(
                          "font-medium text-sm",
                          feature.enabled ? "text-foreground" : "text-muted-foreground"
                        )}>
                          {feature.label}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {feature.description}
                        </p>
                      </div>
                      {feature.enabled && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          Available
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transportation Options */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Train className="h-5 w-5 text-primary" />
                  Transportation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Public Transport */}
                <div>
                  <h4 className="font-semibold mb-2 text-sm">Public Transport</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {transportation.publicTransport.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>

                {/* Parking */}
                <div>
                  <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                    <Car className="h-4 w-4" />
                    Parking
                  </h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {transportation.parking.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>

                {/* Accessibility */}
                <div>
                  <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                    <Accessibility className="h-4 w-4" />
                    Accessibility
                  </h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {transportation.accessibility.map((item, index) => (
                      <li key={index}>• {item}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="shadow-lg bg-gradient-to-r from-primary/5 to-blue-50">
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3 text-sm">Quick Actions</h4>
                <div className="space-y-2">
                  <Button asChild size="sm" className="w-full justify-start">
                    <a href="tel:0390084200">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Office
                    </a>
                  </Button>
                  
                  <Button asChild variant="outline" size="sm" className="w-full justify-start">
                    <a href="/appointments">
                      <Calendar className="h-4 w-4 mr-2" />
                      Book Appointment
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Location Details */}
        <div className="mt-12">
          <Card className="max-w-4xl mx-auto shadow-lg">
            <CardContent className="p-8">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold mb-2">Surrey Hills miNEURO Consulting Suites</h3>
                <p className="text-muted-foreground">
                  Suite 4, Ground Floor, 619 Canterbury Road<br />
                  Surrey Hills VIC 3127, Australia
                </p>
              </div>
              
              <div className={cn(
                "grid gap-6 text-center",
                deviceInfo.isMobile 
                  ? "grid-cols-1" 
                  : "grid-cols-3"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">📞 Contact</h4>
                  <p className="text-sm text-muted-foreground">
                    Phone: (03) 9008 4200<br />
                    Fax: (03) 9923 6688<br />
                    Email: <EMAIL>
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">🕒 Hours</h4>
                  <p className="text-sm text-muted-foreground">
                    Monday - Friday<br />
                    8:30 AM - 5:30 PM<br />
                    Closed weekends
                  </p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">🚗 Access</h4>
                  <p className="text-sm text-muted-foreground">
                    2 min walk from station<br />
                    On-site parking<br />
                    Wheelchair accessible
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default InteractiveMapsSection;
