/**
 * Integration Test Setup Utilities
 * Consolidates duplicate mock patterns and test setup across integration tests
 */

import { vi } from 'vitest';

/**
 * Standard device context mock
 */
export const createDeviceContextMock = () => ({
  useDevice: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenSize: 'desktop'
  })),
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    hasHover: true,
    screenSize: 'desktop',
    orientation: 'landscape',
    pixelRatio: 1,
    isLoaded: true
  })),
  DeviceProvider: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children)
});

/**
 * Standard language context mock
 */
export const createLanguageContextMock = () => ({
  useLanguage: vi.fn(() => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: vi.fn((key: string) => key),
    isRTL: false
  })),
  LanguageProvider: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children)
});

/**
 * Standard router mock
 */
export const createRouterMock = () => ({
  default: {
    useNavigate: vi.fn(() => vi.fn()),
    useLocation: vi.fn(() => ({
      pathname: '/test',
      search: '',
      hash: '',
      state: null
    })),
    BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
    Routes: ({ children }: { children: React.ReactNode }) => children,
    Route: ({ children }: { children: React.ReactNode }) => children,
    Link: ({ children, to }: { children: React.ReactNode; to: string }) => 
      React.createElement('a', { href: to }, children)
  }
});

/**
 * Standard intersection observer mock
 */
export const createIntersectionObserverMock = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  return mockIntersectionObserver;
};

/**
 * Standard resize observer mock
 */
export const createResizeObserverMock = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  return mockResizeObserver;
};

/**
 * Standard window.matchMedia mock
 */
export const createMatchMediaMock = () => {
  return vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
};

/**
 * Setup all standard mocks for integration tests
 */
export function setupIntegrationTestMocks() {
  // Mock contexts
  vi.mock('@/contexts/DeviceContext', () => createDeviceContextMock());
  vi.mock('@/contexts/LanguageContext', () => createLanguageContextMock());
  
  // Mock router
  vi.mock('react-router-dom', () => createRouterMock());
  
  // Mock browser APIs
  global.IntersectionObserver = createIntersectionObserverMock();
  global.ResizeObserver = createResizeObserverMock();
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: createMatchMediaMock(),
  });
  
  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    value: vi.fn(),
    writable: true
  });
  
  // Mock console methods to reduce noise in tests
  global.console = {
    ...console,
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };
}

/**
 * Common test patterns for page integration tests
 */
export const integrationTestPatterns = {
  /**
   * Standard accessibility test pattern
   */
  async testAccessibility(container: HTMLElement) {
    const { axe } = await import('jest-axe');
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  },

  /**
   * Standard rendering test pattern
   */
  async testBasicRendering(component: React.ReactElement, expectedElements: string[]) {
    const { container } = render(component);
    
    // Wait for component to fully render
    await waitFor(() => {
      expectedElements.forEach(element => {
        expect(screen.getByText(element)).toBeInTheDocument();
      });
    });
    
    return container;
  },

  /**
   * Standard responsive test pattern
   */
  async testResponsiveLayout(component: React.ReactElement) {
    // Test desktop layout
    const { rerender } = render(component);
    
    // Mock mobile device
    vi.mocked(require('@/contexts/DeviceContext').useDeviceDetection).mockReturnValue({
      isMobile: true,
      isTablet: false,
      isDesktop: false,
      isTouchDevice: true,
      screenWidth: 375,
      screenHeight: 667,
      orientation: 'portrait',
      deviceType: 'mobile',
      userAgent: 'mobile-agent'
    });
    
    rerender(component);
    
    // Reset to desktop
    vi.mocked(require('@/contexts/DeviceContext').useDeviceDetection).mockReturnValue({
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouchDevice: false,
      screenWidth: 1920,
      screenHeight: 1080,
      orientation: 'landscape',
      deviceType: 'desktop',
      userAgent: 'test-agent'
    });
  }
};

/**
 * Cleanup function for integration tests
 */
export function cleanupIntegrationTest() {
  vi.clearAllMocks();
  vi.resetAllMocks();
}
