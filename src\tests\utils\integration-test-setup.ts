/**
 * Integration Test Setup Utilities
 * Consolidates duplicate mock patterns and test setup across integration tests
 */

import { vi } from 'vitest';

/**
 * Standard device context mock
 */
export const createDeviceContextMock = () => ({
  useDevice: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenSize: 'desktop'
  })),
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    hasHover: true,
    screenSize: 'desktop',
    orientation: 'landscape',
    pixelRatio: 1,
    isLoaded: true
  })),
  DeviceProvider: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children)
});

/**
 * Standard language context mock
 */
export const createLanguageContextMock = () => ({
  useLanguage: vi.fn(() => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: vi.fn((key: string) => key),
    isRTL: false
  })),
  LanguageProvider: ({ children }: { children: React.ReactNode }) => React.createElement('div', {}, children)
});

/**
 * Standard router mock
 */
export const createRouterMock = () => ({
  default: {
    useNavigate: vi.fn(() => vi.fn()),
    useLocation: vi.fn(() => ({
      pathname: '/test',
      search: '',
      hash: '',
      state: null
    })),
    BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
    Routes: ({ children }: { children: React.ReactNode }) => children,
    Route: ({ children }: { children: React.ReactNode }) => children,
    Link: ({ children, to }: { children: React.ReactNode; to: string }) => 
      React.createElement('a', { href: to }, children)
  }
});

/**
 * Standard intersection observer mock
 */
export const createIntersectionObserverMock = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  return mockIntersectionObserver;
};

/**
 * Standard resize observer mock
 */
export const createResizeObserverMock = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  return mockResizeObserver;
};

/**
 * Standard window.matchMedia mock
 */
export const createMatchMediaMock = () => {
  return vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
};

/**
 * Navigation component mocks
 */
export const createNavigationMocks = () => ({
  '@/components/ThemeToggle': {
    default: () => React.createElement('button', {
      'data-testid': 'theme-toggle',
      'aria-label': 'Toggle theme'
    }, '🌙')
  },
  '@/components/LanguageSelector': {
    default: () => React.createElement('div', {
      'data-testid': 'language-selector'
    }, [
      React.createElement('button', {
        key: 'en',
        'data-testid': 'language-en'
      }, 'English'),
      React.createElement('button', {
        key: 'zh',
        'data-testid': 'language-zh'
      }, '中文')
    ])
  },
  '@/components/navigation/MobileNavigation': {
    default: () => React.createElement('nav', {
      'data-testid': 'mobile-navigation',
      role: 'navigation'
    }, [
      React.createElement('button', {
        key: 'toggle',
        'data-testid': 'mobile-menu-toggle',
        'aria-label': 'Toggle mobile menu'
      }, '☰'),
      React.createElement('div', {
        key: 'menu',
        'data-testid': 'mobile-menu'
      }, [
        React.createElement('a', {
          key: 'home',
          href: '/home',
          'data-testid': 'mobile-nav-home'
        }, 'Home'),
        React.createElement('a', {
          key: 'appointments',
          href: '/appointments',
          'data-testid': 'mobile-nav-appointments'
        }, 'Appointments'),
        React.createElement('a', {
          key: 'contact',
          href: '/contact',
          'data-testid': 'mobile-nav-contact'
        }, 'Contact'),
        React.createElement('button', {
          key: 'cta',
          'data-testid': 'mobile-cta-button'
        }, 'Book Appointment')
      ])
    ])
  }
});

/**
 * Setup all standard mocks for integration tests
 */
export function setupIntegrationTestMocks() {
  // Mock contexts
  vi.mock('@/contexts/DeviceContext', () => createDeviceContextMock());
  vi.mock('@/contexts/LanguageContext', () => createLanguageContextMock());

  // Mock router
  vi.mock('react-router-dom', () => createRouterMock());

  // Mock browser APIs
  global.IntersectionObserver = createIntersectionObserverMock();
  global.ResizeObserver = createResizeObserverMock();
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: createMatchMediaMock(),
  });

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    value: vi.fn(),
    writable: true
  });

  // Mock console methods to reduce noise in tests
  global.console = {
    ...console,
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };
}

/**
 * Setup navigation-specific mocks
 */
export function setupNavigationTestMocks() {
  setupIntegrationTestMocks();

  // Add navigation-specific mocks
  const navMocks = createNavigationMocks();
  Object.entries(navMocks).forEach(([path, mock]) => {
    vi.mock(path, () => mock);
  });
}

/**
 * Common test patterns for page integration tests
 */
export const integrationTestPatterns = {
  /**
   * Standard accessibility test pattern
   */
  async testAccessibility(container: HTMLElement) {
    const { axe } = await import('jest-axe');
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  },

  /**
   * Standard rendering test pattern
   */
  async testBasicRendering(component: React.ReactElement, expectedElements: string[]) {
    const { container } = render(component);

    // Wait for component to fully render
    await waitFor(() => {
      expectedElements.forEach(element => {
        expect(screen.getByText(element)).toBeInTheDocument();
      });
    });

    return container;
  },

  /**
   * Standard responsive test pattern
   */
  async testResponsiveLayout(component: React.ReactElement) {
    // Test desktop layout
    const { rerender } = render(component);

    // Mock mobile device
    vi.mocked(require('@/contexts/DeviceContext').useDeviceDetection).mockReturnValue({
      isMobile: true,
      isTablet: false,
      isDesktop: false,
      isTouchDevice: true,
      screenWidth: 375,
      screenHeight: 667,
      orientation: 'portrait',
      deviceType: 'mobile',
      userAgent: 'mobile-agent'
    });

    rerender(component);

    // Reset to desktop
    vi.mocked(require('@/contexts/DeviceContext').useDeviceDetection).mockReturnValue({
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouchDevice: false,
      screenWidth: 1920,
      screenHeight: 1080,
      orientation: 'landscape',
      deviceType: 'desktop',
      userAgent: 'test-agent'
    });
  },

  /**
   * Comprehensive page test pattern
   */
  async testPageComprehensively(
    pageName: string,
    pageComponent: React.ComponentType,
    config: {
      expectedSections: string[];
      interactiveElements: string[];
      performanceThresholds: {
        renderTime: number;
        memoryUsage: number;
      };
    }
  ) {
    const { testPageComprehensively, ErrorTracker } = await import('@/tests/utils/enhanced-test-helpers');

    const results = await testPageComprehensively({
      component: pageComponent,
      name: pageName,
      expectedSections: config.expectedSections,
      interactiveElements: config.interactiveElements,
      performanceThresholds: config.performanceThresholds
    });

    // Log results for analysis
    if (import.meta.env.DEV) {
      console.log(`🔍 ${pageName} Page Test Results:`, {
        rendering: results.rendering,
        content: results.content,
        interactions: results.interactions,
        accessibility: results.accessibility,
        performance: results.performance,
        errorHandling: results.errorHandling,
        summary: results.summary
      });
    }

    // Report any errors found
    if (results.allErrors.length > 0) {
      if (import.meta.env.DEV) {
        console.warn(`⚠️ Errors found in ${pageName} page:`, results.allErrors);
      }

      // Track errors for systematic resolution
      results.allErrors.forEach(error => {
        ErrorTracker.addError(pageName, 'Page', error);
      });
    }

    // Basic assertions - page should at least render
    expect(results.rendering).toBe(true);

    // Content should be present (either success or error boundary)
    expect(results.content).toBe(true);

    return results;
  },

  /**
   * Standard error boundary test pattern
   */
  testErrorBoundaryHandling(component: React.ReactElement) {
    const { container } = render(component);

    // Test main page structure (handle both success and error scenarios)
    const mainElements = screen.queryAllByRole('main');
    const errorBoundary = screen.queryAllByText(/something went wrong/i);

    if (mainElements.length > 0) {
      // Success scenario - component rendered correctly
      expect(mainElements.length).toBeGreaterThan(0);
      return { success: true, hasError: false };
    } else if (errorBoundary.length > 0) {
      // Error scenario - error boundary caught an error
      expect(errorBoundary.length).toBeGreaterThan(0);
      return { success: false, hasError: true };
    } else {
      throw new Error('Neither main elements nor error boundary found');
    }
  },

  /**
   * Standard layout integration test pattern
   */
  async testLayoutIntegration(component: React.ReactElement) {
    render(component);

    // Test header integration
    const header = screen.queryByRole('banner') ||
                  screen.queryByTestId('page-header');
    if (header) {
      expect(header).toBeInTheDocument();
    }

    // Test footer integration (if present)
    const footer = screen.queryByRole('contentinfo') ||
                  screen.queryByTestId('page-footer');
    if (footer) {
      expect(footer).toBeInTheDocument();
    }

    // Test navigation integration
    const navigationElements = screen.queryAllByRole('navigation');
    const mainNavigation = screen.queryByTestId('main-navigation');
    const hasNavigation = navigationElements.length > 0 || mainNavigation !== null;

    return { hasHeader: !!header, hasFooter: !!footer, hasNavigation };
  },

  /**
   * Standard content section test pattern
   */
  async testContentSections(expectedSections: string[]) {
    await waitFor(() => {
      expectedSections.forEach(section => {
        const elements = screen.queryAllByText(new RegExp(section, 'i'));
        if (elements.length === 0) {
          // Try alternative selectors
          const byTestId = screen.queryByTestId(section.toLowerCase().replace(/\s+/g, '-'));
          const byRole = screen.queryByRole('heading', { name: new RegExp(section, 'i') });

          expect(elements.length > 0 || byTestId || byRole).toBeTruthy();
        }
      });
    });
  }
};

/**
 * Cleanup function for integration tests
 */
export function cleanupIntegrationTest() {
  vi.clearAllMocks();
  vi.resetAllMocks();
}
