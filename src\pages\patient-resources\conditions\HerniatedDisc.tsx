import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { herniatedDiscData } from '@/data/conditions/herniateddisc';

/**
 * Herniated Disc Component
 * 
 * Comprehensive guide for herniated disc using modular,
 * data-driven architecture for maintainability
 */

const HerniatedDisc: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Herniated Disc - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={herniatedDiscData.hero.title}
          subtitle={herniatedDiscData.hero.subtitle}
          backgroundImage={herniatedDiscData.hero.backgroundImage}
          badge={herniatedDiscData.hero.badge}
        />

        <ConditionQuickFacts facts={herniatedDiscData.quickFacts} />

        <ConditionOverviewSection
          title={herniatedDiscData.overview.title}
          description={herniatedDiscData.overview.description}
          keyPoints={herniatedDiscData.overview.keyPoints}
          imageSrc={herniatedDiscData.overview.imageSrc}
          imageAlt={herniatedDiscData.overview.imageAlt}
          imageCaption={herniatedDiscData.overview.imageCaption}
        />

        <ConditionCauses
          causes={herniatedDiscData.causes}
          riskFactors={herniatedDiscData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={herniatedDiscData.symptoms}
          warningSigns={herniatedDiscData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={herniatedDiscData.conservativeTreatments}
          surgicalOptions={herniatedDiscData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

HerniatedDisc.displayName = 'HerniatedDisc';

export default HerniatedDisc;
