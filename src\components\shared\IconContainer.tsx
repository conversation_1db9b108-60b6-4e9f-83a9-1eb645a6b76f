import React from 'react';

import { cn } from '@/lib/utils';
import { getSizeClass, getColorClass, getShapeClass, type SizeVariant, type ColorVariant, type ShapeVariant } from '@/lib/style-utilities';

interface IconContainerProps {
  icon: React.ReactNode;
  size?: SizeVariant;
  variant?: ColorVariant;
  shape?: ShapeVariant;
  className?: string;
}

const IconContainer: React.FC<IconContainerProps> = ({
  icon,
  size = 'md',
  variant = 'primary',
  shape = 'circle',
  className = ''
}) => {
  const sizeClass = getSizeClass(size || 'md', 'icon');
  const variantClass = getColorClass(variant || 'primary', 'background');
  const shapeClass = getShapeClass(shape || 'circle');

  return (
    <div className={cn(
      sizeClass,
      variantClass,
      shapeClass,
      'flex items-center justify-center flex-shrink-0',
      className
    )}>
      {icon}
    </div>
  );
};

export default IconContainer;
