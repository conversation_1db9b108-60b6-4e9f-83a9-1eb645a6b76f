import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TelehealthOption {
  platform: string;
  icon: LucideIcon;
  description: string;
}

interface TelehealthSectionProps {
  title: string;
  subtitle: string;
  description: string[];
  options: TelehealthOption[];
  suitableFor: string[];
  requirements: string[];
}

const TelehealthSection: React.FC<TelehealthSectionProps> = ({
  title,
  subtitle,
  description,
  options,
  suitableFor,
  requirements
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground mb-6">
            {subtitle}
          </p>
          <div className="max-w-3xl mx-auto space-y-4">
            {description.map((paragraph, index) => (
              <p key={index} className="text-muted-foreground">
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        <div className={cn(
          "grid gap-8 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-3"
        )}>
          {/* Platform Options */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-primary">
                Available Platforms
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {options.map((option, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <option.icon className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">{option.platform}</p>
                      <p className="text-sm text-muted-foreground">
                        {option.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Suitable For */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-primary">
                Suitable For
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {suitableFor.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      ✓
                    </Badge>
                    <span className="text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Requirements */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-primary">
                Technical Requirements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requirements.map((requirement, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Badge variant="outline" className="text-xs mt-1">
                      •
                    </Badge>
                    <span className="text-sm">{requirement}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto shadow-lg bg-gradient-to-r from-primary/10 to-primary/5">
            <CardContent className="p-8">
              <h3 className="text-xl font-bold mb-4">
                Book Your Telehealth Consultation
              </h3>
              <p className="text-muted-foreground mb-6">
                When booking your appointment, please specify that you would prefer a telehealth consultation 
                and indicate your preferred platform.
              </p>
              <div className="flex flex-wrap justify-center gap-2">
                <Badge variant="secondary">Convenient</Badge>
                <Badge variant="secondary">Safe</Badge>
                <Badge variant="secondary">Accessible</Badge>
                <Badge variant="secondary">Time-saving</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TelehealthSection;
