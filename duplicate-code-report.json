{"timestamp": "2025-07-06T08:02:54.219Z", "filesAnalyzed": 389, "blocksAnalyzed": 1088, "duplicatesFound": 35, "similarPatternsFound": 270, "duplicates": [{"hash": "bf8fbea1810da582d5a40d4758a036ec", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (timerRef.current) {\r\n        clearTimeout(timerRef.current);\r\n      }", "normalized": "if (timerRef.current) { clearTimeout(timerRef.current); }", "hash": "bf8fbea1810da582d5a40d4758a036ec", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\AppointmentForm.tsx", "line": 62, "size": 73}, {"type": "pattern", "content": "if (timerRef.current) {\r\n      clearTimeout(timerRef.current);\r\n    }", "normalized": "if (timerRef.current) { clearTimeout(timerRef.current); }", "hash": "bf8fbea1810da582d5a40d4758a036ec", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\AppointmentForm.tsx", "line": 83, "size": 69}], "totalSize": 142, "files": ["components\\AppointmentForm.tsx"]}, {"hash": "425c8359e665767714530add18678382", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (onRetry) {\r\n    actions.push({\r\n      label: 'Try Again',\r\n      onClick: onRetry,\r\n      icon: RefreshCw\r\n    }", "normalized": "if (onRetry) { actions.push({ VAR: STRING, VAR: onRetry, VAR: RefreshCw }", "hash": "425c8359e665767714530add18678382", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\EmptyState.tsx", "line": 313, "size": 116}, {"type": "pattern", "content": "if (onRetry) {\r\n    actions.push({\r\n      label: 'Refresh Page',\r\n      onClick: onRetry,\r\n      icon: RefreshCw\r\n    }", "normalized": "if (onRetry) { actions.push({ VAR: STRING, VAR: onRetry, VAR: RefreshCw }", "hash": "425c8359e665767714530add18678382", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\EmptyState.tsx", "line": 335, "size": 119}], "totalSize": 235, "files": ["components\\EmptyState.tsx"]}, {"hash": "dc1f761fc1aca93ec59966b6359fd8a7", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (animationTimeoutRef.current) {\r\n      clearTimeout(animationTimeoutRef.current);\r\n    }", "normalized": "if (animationTimeoutRef.current) { clearTimeout(animationTimeoutRef.current); }", "hash": "dc1f761fc1aca93ec59966b6359fd8a7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\TestimonialsSection.tsx", "line": 116, "size": 91}, {"type": "pattern", "content": "if (animationTimeoutRef.current) {\r\n      clearTimeout(animationTimeoutRef.current);\r\n    }", "normalized": "if (animationTimeoutRef.current) { clearTimeout(animationTimeoutRef.current); }", "hash": "dc1f761fc1aca93ec59966b6359fd8a7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\TestimonialsSection.tsx", "line": 116, "size": 91}, {"type": "pattern", "content": "if (animationTimeoutRef.current) {\r\n        clearTimeout(animationTimeoutRef.current);\r\n      }", "normalized": "if (animationTimeoutRef.current) { clearTimeout(animationTimeoutRef.current); }", "hash": "dc1f761fc1aca93ec59966b6359fd8a7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\TestimonialsSection.tsx", "line": 154, "size": 95}], "totalSize": 277, "files": ["components\\TestimonialsSection.tsx"]}, {"hash": "ceda6f1db71b4dfb9abd258f2e1d901c", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (isDarkMode) {\r\n        document.documentElement.classList.add(\"dark\");\r\n      }", "normalized": "if (isDarkMode) { document.documentElement.classList.add(STRING); }", "hash": "ceda6f1db71b4dfb9abd258f2e1d901c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ThemeToggle.tsx", "line": 18, "size": 83}, {"type": "pattern", "content": "if (isDarkMode) {\r\n        document.documentElement.classList.add(\"dark\");\r\n      }", "normalized": "if (isDarkMode) { document.documentElement.classList.add(STRING); }", "hash": "ceda6f1db71b4dfb9abd258f2e1d901c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ThemeToggle.tsx", "line": 18, "size": 83}], "totalSize": 166, "files": ["components\\ThemeToggle.tsx"]}, {"hash": "239be497ba6982d39d5e741b409a6dc3", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\carousel.tsx", "line": 38, "size": 91}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\chart.tsx", "line": 31, "size": 94}], "totalSize": 185, "files": ["components\\ui\\carousel.tsx", "components\\ui\\chart.tsx"]}, {"hash": "0f951da3f322f1137775afa66890b4c2", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (savedTheme === \"dark\" || savedTheme === \"light\") {\r\n          setTheme(savedTheme);\r\n        }", "normalized": "if (VAR === STRING || VAR === STRING) { setTheme(savedTheme); }", "hash": "0f951da3f322f1137775afa66890b4c2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\sonner.tsx", "line": 14, "size": 98}, {"type": "pattern", "content": "if (savedTheme === \"dark\" || savedTheme === \"light\") {\r\n        setTheme(savedTheme);\r\n      }", "normalized": "if (VAR === STRING || VAR === STRING) { setTheme(savedTheme); }", "hash": "0f951da3f322f1137775afa66890b4c2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\sonner.tsx", "line": 30, "size": 94}], "totalSize": 192, "files": ["components\\ui\\sonner.tsx"]}, {"hash": "8dd39f84b9bf70451e1828c1df0f9d45", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useDeviceDetection must be used within a DeviceProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\DeviceContext.tsx", "line": 149, "size": 114}, {"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useDeviceLoaded must be used within a DeviceProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\DeviceContext.tsx", "line": 163, "size": 111}, {"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useLanguage must be used within a LanguageProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\LanguageContext.tsx", "line": 76, "size": 109}], "totalSize": 334, "files": ["contexts\\DeviceContext.tsx", "contexts\\LanguageContext.tsx"]}, {"hash": "cd507924d0d93fb1d0eeceaa250ae28d", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = null;\n    }", "normalized": "if (timeoutRef.current) { clearTimeout(timeoutRef.current); timeoutRef.VAR = null; }", "hash": "cd507924d0d93fb1d0eeceaa250ae28d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\hooks\\useBaseAsyncState.ts", "line": 71, "size": 104}, {"type": "pattern", "content": "if (timeoutRef.current) {\n          clearTimeout(timeoutRef.current);\n          timeoutRef.current = null;\n        }", "normalized": "if (timeoutRef.current) { clearTimeout(timeoutRef.current); timeoutRef.VAR = null; }", "hash": "cd507924d0d93fb1d0eeceaa250ae28d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\hooks\\useBaseAsyncState.ts", "line": 100, "size": 116}, {"type": "pattern", "content": "if (timeoutRef.current) {\n          clearTimeout(timeoutRef.current);\n          timeoutRef.current = null;\n        }", "normalized": "if (timeoutRef.current) { clearTimeout(timeoutRef.current); timeoutRef.VAR = null; }", "hash": "cd507924d0d93fb1d0eeceaa250ae28d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\hooks\\useBaseAsyncState.ts", "line": 100, "size": 116}], "totalSize": 336, "files": ["hooks\\useBaseAsyncState.ts"]}, {"hash": "6042045156fff1827fda99187db251e5", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (isProduction) {\r\n      // Ensure debug flags are disabled in production\r\n      if (config.DEBUG_MODE) {\r\n        errors.push('DEBUG_MODE must be false in production');\r\n      }", "normalized": "if (isProduction) {", "hash": "6042045156fff1827fda99187db251e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\env-validation.ts", "line": 171, "size": 180}, {"type": "pattern", "content": "if (isProduction) {\r\n        // Warn about missing optional but recommended variables\r\n        if (!config.GOOGLE_ANALYTICS_ID) {\r\n          warnings.push('Google Analytics ID not configured - analytics tracking disabled');\r\n        }", "normalized": "if (isProduction) {", "hash": "6042045156fff1827fda99187db251e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\env-validation.ts", "line": 187, "size": 234}], "totalSize": 414, "files": ["lib\\env-validation.ts"]}, {"hash": "6cecc6b9e6806ac3fdae536461c399bc", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (!data['@context']) {\r\n    errors.push('Missing required @context field');\r\n  }", "normalized": "if (!data[STRING]) { errors.push(STRING); }", "hash": "6cecc6b9e6806ac3fdae536461c399bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\seo.ts", "line": 389, "size": 82}, {"type": "pattern", "content": "if (!data['@type']) {\r\n    errors.push('Missing required @type field');\r\n  }", "normalized": "if (!data[STRING]) { errors.push(STRING); }", "hash": "6cecc6b9e6806ac3fdae536461c399bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\seo.ts", "line": 395, "size": 76}], "totalSize": 158, "files": ["lib\\seo.ts"]}, {"hash": "ced9e2448c7e5725d848f260139f1178", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "for (const key of keys) {\r\n    if (current && typeof current === 'object' && key in current) {\r\n      current = (current as Record<string, unknown>)[key];\r\n    }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = (current as Record<string, unknown>)[key]; }", "hash": "ced9e2448c7e5725d848f260139f1178", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\type-safety.ts", "line": 67, "size": 161}, {"type": "pattern", "content": "for (const key of keys) {\r\n    if (current && typeof current === 'object' && key in current) {\r\n      current = (current as Record<string, unknown>)[key];\r\n    }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = (current as Record<string, unknown>)[key]; }", "hash": "ced9e2448c7e5725d848f260139f1178", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\types\\translations.ts", "line": 362, "size": 161}], "totalSize": 322, "files": ["lib\\type-safety.ts", "types\\translations.ts"]}, {"hash": "974d5d4041d21ccf411a71e4b00afc39", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (e.key === \"ArrowLeft\") {\r\n        navigateGallery(\"prev\");\r\n      }", "normalized": "if (e.VAR === STRING) { navigateGallery(STRING); }", "hash": "974d5d4041d21ccf411a71e4b00afc39", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\Gallery.tsx", "line": 157, "size": 71}, {"type": "pattern", "content": "if (e.key === \"ArrowRight\") {\r\n        navigateGallery(\"next\");\r\n      }", "normalized": "if (e.VAR === STRING) { navigateGallery(STRING); }", "hash": "974d5d4041d21ccf411a71e4b00afc39", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\Gallery.tsx", "line": 159, "size": 72}], "totalSize": 143, "files": ["pages\\Gallery.tsx"]}, {"hash": "f425f042232aa0aa9b20895853547937", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "for (const key of keys) {\r\n      if (current && typeof current === 'object' && key in current) {\r\n        current = current[key];\r\n      }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = current[key]; }", "hash": "f425f042232aa0aa9b20895853547937", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\Index.tsx", "line": 34, "size": 138}, {"type": "pattern", "content": "for (const key of keys) {\r\n      if (current && typeof current === 'object' && key in current) {\r\n        current = current[key];\r\n      }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = current[key]; }", "hash": "f425f042232aa0aa9b20895853547937", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\Index.tsx", "line": 34, "size": 138}], "totalSize": 276, "files": ["pages\\Index.tsx"]}, {"hash": "17480afe1cb8459c8a0c21dd7a94c77e", "type": "pattern", "count": 4, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "totalSize": 416, "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"]}, {"hash": "f6454f2e02b32ed26132c949c2fdafc4", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (error.code === 'REQUEST_ABORTED') {\r\n      return false;\r\n    }", "normalized": "if (error.VAR === STRING) { return false; }", "hash": "f6454f2e02b32ed26132c949c2fdafc4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 181, "size": 67}, {"type": "pattern", "content": "if (error.code === 'REQUEST_ABORTED') {\r\n      return false;\r\n    }", "normalized": "if (error.VAR === STRING) { return false; }", "hash": "f6454f2e02b32ed26132c949c2fdafc4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 181, "size": 67}], "totalSize": 134, "files": ["services\\api\\errors\\ServiceErrorHandler.ts"]}, {"hash": "40b8223313832bae8c3df5e57893f9ca", "type": "pattern", "count": 4, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "totalSize": 400, "files": ["services\\api\\network\\NetworkUtils.ts", "services\\index.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"hash": "68336fc27997c50d67fdd7346636a14e", "type": "pattern", "count": 6, "blocks": [{"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}, {"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}, {"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}, {"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}, {"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}, {"type": "pattern", "content": "if (!isInitialized) {\r\n    initializeServices();\r\n  }", "normalized": "if (!isInitialized) { initializeServices(); }", "hash": "68336fc27997c50d67fdd7346636a14e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 99, "size": 53}], "totalSize": 318, "files": ["services\\index.ts"]}, {"hash": "c566a79b729bf8098688667c6dba0ecd", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "totalSize": 324, "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"hash": "246237cd25adccd60f04549e2ab7695a", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "totalSize": 332, "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"hash": "e7707f12b955a1a42713b2f894bd38c7", "type": "pattern", "count": 6, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          expect(errorBoundary[0]).toBeInTheDocument();\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) { expect(errorBoundary[NUMBER]).toBeInTheDocument(); }", "hash": "e7707f12b955a1a42713b2f894bd38c7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 153, "size": 99}], "totalSize": 594, "files": ["tests\\integration\\components\\Navigation.integration.test.tsx"]}, {"hash": "659b212198a65f2d6da1e91986f6315d", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (languageSelector) {\r\n          expect(languageSelector).toBeInTheDocument();\r\n        }", "normalized": "if (languageSelector) { expect(languageSelector).toBeInTheDocument(); }", "hash": "659b212198a65f2d6da1e91986f6315d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 174, "size": 91}, {"type": "pattern", "content": "if (languageSelector) {\r\n          expect(languageSelector).toBeInTheDocument();\r\n        }", "normalized": "if (languageSelector) { expect(languageSelector).toBeInTheDocument(); }", "hash": "659b212198a65f2d6da1e91986f6315d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 174, "size": 91}], "totalSize": 182, "files": ["tests\\integration\\components\\Navigation.integration.test.tsx"]}, {"hash": "4c9566f147373791d9f2ea2197056f45", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (focusableElements.length > 0) {\r\n          focusableElements[0].focus();\r\n          expect(document.activeElement).toBe(focusableElements[0]);\r\n        }", "normalized": "if (focusableElements.length > NUMBER) { focusableElements[NUMBER].focus(); expect(document.activeElement).toBe(focusableElements[NUMBER]); }", "hash": "4c9566f147373791d9f2ea2197056f45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 346, "size": 157}, {"type": "pattern", "content": "if (focusableElements.length > 0) {\r\n        focusableElements[0].focus();\r\n        expect(document.activeElement).toBe(focusableElements[0]);\r\n      }", "normalized": "if (focusableElements.length > NUMBER) { focusableElements[NUMBER].focus(); expect(document.activeElement).toBe(focusableElements[NUMBER]); }", "hash": "4c9566f147373791d9f2ea2197056f45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 430, "size": 151}], "totalSize": 308, "files": ["tests\\integration\\components\\Navigation.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"hash": "1ace357e905fd702ef9c556bd5c7cd58", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "for (let i = 0; i < 3; i++) {\r\n        const { unmount }", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { const { unmount }", "hash": "1ace357e905fd702ef9c556bd5c7cd58", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 278, "size": 56}, {"type": "pattern", "content": "for (let i = 0; i < 3; i++) {\r\n        const { unmount }", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { const { unmount }", "hash": "1ace357e905fd702ef9c556bd5c7cd58", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 309, "size": 56}], "totalSize": 112, "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"hash": "5da3c1e89d61e9d13043291fae4286a3", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Navigation component error details:', {\r\n          message: error instanceof Error ? error.message : 'Unknown error',\r\n          stack: error instanceof Error ? error.stack : 'No stack trace'\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, { VAR: error instanceof Error ? error.VAR : STRING, VAR: error instanceof Error ? error.VAR : STRING }", "hash": "5da3c1e89d61e9d13043291fae4286a3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 333, "size": 255}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Language component error details:', {\r\n          message: error instanceof Error ? error.message : 'Unknown error',\r\n          stack: error instanceof Error ? error.stack : 'No stack trace'\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, { VAR: error instanceof Error ? error.VAR : STRING, VAR: error instanceof Error ? error.VAR : STRING }", "hash": "5da3c1e89d61e9d13043291fae4286a3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 345, "size": 253}], "totalSize": 508, "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"hash": "d6384b80c2a4ec7c3fa07c3e6c0f9bda", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (retryButton) {\r\n          expect(retryButton).toBeInTheDocument();\r\n        }", "normalized": "if (retryButton) { expect(retryButton).toBeInTheDocument(); }", "hash": "d6384b80c2a4ec7c3fa07c3e6c0f9bda", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\errors\\NetworkErrors.integration.test.tsx", "line": 339, "size": 81}, {"type": "pattern", "content": "if (retryButton) {\r\n        expect(retryButton).toBeInTheDocument();\r\n      }", "normalized": "if (retryButton) { expect(retryButton).toBeInTheDocument(); }", "hash": "d6384b80c2a4ec7c3fa07c3e6c0f9bda", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\errors\\NetworkErrors.integration.test.tsx", "line": 369, "size": 77}], "totalSize": 158, "files": ["tests\\integration\\errors\\NetworkErrors.integration.test.tsx"]}, {"hash": "0fe41354a58534d67b041cfa2e9a9be4", "type": "pattern", "count": 12, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test responsive elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test mobile-specific elements (handle multiple menu buttons)\r\n        const mobileNavElement = screen.queryByTestId('mobile-navigation');\r\n        const menuButtons = screen.queryAllByRole('button', { name: /menu/i }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 208, "size": 374}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test device-specific elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 284, "size": 157}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test semantic structure\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test for headings\r\n        const headings = screen.queryAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n\r\n        // Test for H1 elements (may not exist in error boundary)\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements are optional in this context\r\n\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 378, "size": 538}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test for H1 elements\r\n        const headings = screen.getAllByRole('heading');\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements may not exist in all page states\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 401, "size": 377}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "totalSize": 3364, "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"hash": "84a95d0135fb8d78d9fcd45050232de6", "type": "pattern", "count": 11, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides responsive fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 222, "size": 171}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides language fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 247, "size": 169}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides device fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 287, "size": 167}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides semantic structure\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n        expect(screen.getByRole('heading', { name: /something went wrong/i }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 392, "size": 239}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary has H3 heading\r\n        const headings = screen.getAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 409, "size": 210}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "totalSize": 1939, "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"hash": "899d6817306f6b6edea084280578d7b3", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Appointments Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 310, "size": 153}, {"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Contact Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 251, "size": 148}, {"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 FINAL CORE PAGES ERROR SUMMARY:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 303, "size": 152}], "totalSize": 453, "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"hash": "d9106790761b3d5f8afdefff70b19999", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 105, "size": 72}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 88, "size": 74}], "totalSize": 146, "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"hash": "2a08772ef3926aa17339d5607514cc60", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n          if (import.meta.env.DEV) {\r\n            console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 118, "size": 138}, {"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n            if (import.meta.env.DEV) {\r\n              console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 102, "size": 142}], "totalSize": 280, "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"hash": "fe181ec92fa80c04c50f318c14e07016", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 151, "size": 75}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 138, "size": 77}], "totalSize": 152, "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"hash": "df324d17579e9969a45e669eec48f0be", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (header) {\r\n        expect(header).toBeInTheDocument();\r\n      }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 141, "size": 67}, {"type": "pattern", "content": "if (header) {\n      expect(header).toBeInTheDocument();\n    }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 372, "size": 61}], "totalSize": 128, "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "if (footer) {\r\n        expect(footer).toBeInTheDocument();\r\n      }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 148, "size": 67}, {"type": "pattern", "content": "if (footer) {\n      expect(footer).toBeInTheDocument();\n    }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 379, "size": 61}], "totalSize": 128, "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"hash": "087489c93a45ffbeeda51a60af97fccf", "type": "pattern", "count": 2, "blocks": [{"type": "pattern", "content": "for (let i = 0; i < 2000; i++) {\r\n        monitor.startMeasure(`metric-${i}", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { monitor.startMeasure(`metric-${i}", "hash": "087489c93a45ffbeeda51a60af97fccf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\performance.test.ts", "line": 351, "size": 75}, {"type": "pattern", "content": "for (let i = 0; i < 1000; i++) {\r\n        monitor.startMeasure(`metric-${i}", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { monitor.startMeasure(`metric-${i}", "hash": "087489c93a45ffbeeda51a60af97fccf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\performance.test.ts", "line": 365, "size": 75}], "totalSize": 150, "files": ["tests\\utils\\performance.test.ts"]}, {"hash": "59580b259f01eca763eec2bf384f52b8", "type": "pattern", "count": 3, "blocks": [{"type": "pattern", "content": "if (check.data) {\n            console.log(`          Data: ${JSON.stringify(check.data, null, 2)}", "normalized": "if (check.data) { console.log(` VAR: ${JSON.stringify(check.data, null, NUMBER)}", "hash": "59580b259f01eca763eec2bf384f52b8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 60, "size": 97}, {"type": "pattern", "content": "if (check.data) {\n            console.log(`          Data: ${JSON.stringify(check.data, null, 2)}", "normalized": "if (check.data) { console.log(` VAR: ${JSON.stringify(check.data, null, NUMBER)}", "hash": "59580b259f01eca763eec2bf384f52b8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 60, "size": 97}, {"type": "pattern", "content": "if (check.data) {\n          console.log(`          Data: ${JSON.stringify(check.data, null, 2)}", "normalized": "if (check.data) { console.log(` VAR: ${JSON.stringify(check.data, null, NUMBER)}", "hash": "59580b259f01eca763eec2bf384f52b8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 85, "size": 95}], "totalSize": 289, "files": ["tests\\utils\\test-logging.ts"]}], "similarPatterns": [{"similarity": 1, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\carousel.tsx", "line": 38, "size": 91}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\chart.tsx", "line": 31, "size": 94}], "files": ["components\\ui\\carousel.tsx", "components\\ui\\chart.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useDeviceDetection must be used within a DeviceProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\DeviceContext.tsx", "line": 149, "size": 114}, {"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useLanguage must be used within a LanguageProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\LanguageContext.tsx", "line": 76, "size": 109}], "files": ["contexts\\DeviceContext.tsx", "contexts\\LanguageContext.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useDeviceLoaded must be used within a DeviceProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\DeviceContext.tsx", "line": 163, "size": 111}, {"type": "pattern", "content": "if (context === undefined) {\r\n    throw new Error('useLanguage must be used within a LanguageProvider');\r\n  }", "normalized": "if (VAR === undefined) { throw new Error(STRING); }", "hash": "8dd39f84b9bf70451e1828c1df0f9d45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\contexts\\LanguageContext.tsx", "line": 76, "size": 109}], "files": ["contexts\\DeviceContext.tsx", "contexts\\LanguageContext.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "for (const key of keys) {\r\n    if (current && typeof current === 'object' && key in current) {\r\n      current = (current as Record<string, unknown>)[key];\r\n    }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = (current as Record<string, unknown>)[key]; }", "hash": "ced9e2448c7e5725d848f260139f1178", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\type-safety.ts", "line": 67, "size": 161}, {"type": "pattern", "content": "for (const key of keys) {\r\n    if (current && typeof current === 'object' && key in current) {\r\n      current = (current as Record<string, unknown>)[key];\r\n    }", "normalized": "for (const key of keys) { if (current && typeof VAR === STRING && key in current) { VAR = (current as Record<string, unknown>)[key]; }", "hash": "ced9e2448c7e5725d848f260139f1178", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\types\\translations.ts", "line": 362, "size": 161}], "files": ["lib\\type-safety.ts", "types\\translations.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}], "files": ["services\\api\\network\\NetworkUtils.ts", "services\\index.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\index.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\index.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (focusableElements.length > 0) {\r\n          focusableElements[0].focus();\r\n          expect(document.activeElement).toBe(focusableElements[0]);\r\n        }", "normalized": "if (focusableElements.length > NUMBER) { focusableElements[NUMBER].focus(); expect(document.activeElement).toBe(focusableElements[NUMBER]); }", "hash": "4c9566f147373791d9f2ea2197056f45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 346, "size": 157}, {"type": "pattern", "content": "if (focusableElements.length > 0) {\r\n        focusableElements[0].focus();\r\n        expect(document.activeElement).toBe(focusableElements[0]);\r\n      }", "normalized": "if (focusableElements.length > NUMBER) { focusableElements[NUMBER].focus(); expect(document.activeElement).toBe(focusableElements[NUMBER]); }", "hash": "4c9566f147373791d9f2ea2197056f45", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 430, "size": 151}], "files": ["tests\\integration\\components\\Navigation.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "for (let i = 0; i < 3; i++) {\r\n        const { unmount }", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { const { unmount }", "hash": "1ace357e905fd702ef9c556bd5c7cd58", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 278, "size": 56}, {"type": "pattern", "content": "for (let i = 0; i < 3; i++) {\r\n        const { unmount }", "normalized": "for (let VAR = NUMBER; i < NUMBER; i++) { const { unmount }", "hash": "1ace357e905fd702ef9c556bd5c7cd58", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 309, "size": 56}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Navigation component error details:', {\r\n          message: error instanceof Error ? error.message : 'Unknown error',\r\n          stack: error instanceof Error ? error.stack : 'No stack trace'\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, { VAR: error instanceof Error ? error.VAR : STRING, VAR: error instanceof Error ? error.VAR : STRING }", "hash": "5da3c1e89d61e9d13043291fae4286a3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 333, "size": 255}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Language component error details:', {\r\n          message: error instanceof Error ? error.message : 'Unknown error',\r\n          stack: error instanceof Error ? error.stack : 'No stack trace'\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, { VAR: error instanceof Error ? error.VAR : STRING, VAR: error instanceof Error ? error.VAR : STRING }", "hash": "5da3c1e89d61e9d13043291fae4286a3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 345, "size": 253}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test responsive elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test mobile-specific elements (handle multiple menu buttons)\r\n        const mobileNavElement = screen.queryByTestId('mobile-navigation');\r\n        const menuButtons = screen.queryAllByRole('button', { name: /menu/i }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 208, "size": 374}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test device-specific elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 284, "size": 157}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test semantic structure\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test for headings\r\n        const headings = screen.queryAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n\r\n        // Test for H1 elements (may not exist in error boundary)\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements are optional in this context\r\n\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 378, "size": 538}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test for H1 elements\r\n        const headings = screen.getAllByRole('heading');\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements may not exist in all page states\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 401, "size": 377}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides responsive fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 222, "size": 171}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides language fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 247, "size": 169}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides device fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 287, "size": 167}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides semantic structure\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n        expect(screen.getByRole('heading', { name: /something went wrong/i }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 392, "size": 239}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary has H3 heading\r\n        const headings = screen.getAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 409, "size": 210}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 128, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Appointments Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 310, "size": 153}, {"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Contact Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 251, "size": 148}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Appointments Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 310, "size": 153}, {"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 FINAL CORE PAGES ERROR SUMMARY:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 303, "size": 152}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test responsive elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test mobile-specific elements (handle multiple menu buttons)\r\n        const mobileNavElement = screen.queryByTestId('mobile-navigation');\r\n        const menuButtons = screen.queryAllByRole('button', { name: /menu/i }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 208, "size": 374}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test device-specific elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 284, "size": 157}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test semantic structure\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test for headings\r\n        const headings = screen.queryAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n\r\n        // Test for H1 elements (may not exist in error boundary)\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements are optional in this context\r\n\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 378, "size": 538}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test for H1 elements\r\n        const headings = screen.getAllByRole('heading');\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements may not exist in all page states\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 401, "size": 377}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides responsive fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 222, "size": 171}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides language fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 247, "size": 169}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides device fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 287, "size": 167}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides semantic structure\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n        expect(screen.getByRole('heading', { name: /something went wrong/i }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 392, "size": 239}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary has H3 heading\r\n        const headings = screen.getAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 409, "size": 210}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n          // Error scenario - error boundary is working\r\n          expect(errorBoundary.length).toBeGreaterThan(0);\r\n        }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 119, "size": 159}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 Contact Page Error Summary:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 251, "size": 148}, {"type": "pattern", "content": "if (errorSummary.totalErrors > 0) {\r\n      if (import.meta.env.DEV) {\r\n        console.log('🔍 FINAL CORE PAGES ERROR SUMMARY:', errorSummary);\r\n      }", "normalized": "if (errorSummary.totalErrors > NUMBER) { if (import.meta.env.DEV) { console.log(STRING, errorSummary); }", "hash": "899d6817306f6b6edea084280578d7b3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 303, "size": 152}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 105, "size": 72}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 88, "size": 74}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n          if (import.meta.env.DEV) {\r\n            console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 118, "size": 138}, {"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n            if (import.meta.env.DEV) {\r\n              console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 102, "size": 142}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 151, "size": 75}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 138, "size": 77}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (header) {\r\n        expect(header).toBeInTheDocument();\r\n      }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 141, "size": 67}, {"type": "pattern", "content": "if (header) {\n      expect(header).toBeInTheDocument();\n    }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 372, "size": 61}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (footer) {\r\n        expect(footer).toBeInTheDocument();\r\n      }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 148, "size": 67}, {"type": "pattern", "content": "if (footer) {\n      expect(footer).toBeInTheDocument();\n    }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 379, "size": 61}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test responsive elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test mobile-specific elements (handle multiple menu buttons)\r\n        const mobileNavElement = screen.queryByTestId('mobile-navigation');\r\n        const menuButtons = screen.queryAllByRole('button', { name: /menu/i }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 208, "size": 374}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides responsive fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 222, "size": 171}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides language fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 247, "size": 169}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test device-specific elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 284, "size": 157}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides device fallback\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 287, "size": 167}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary caught an error\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 51, "size": 158}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test semantic structure\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test for headings\r\n        const headings = screen.queryAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n\r\n        // Test for H1 elements (may not exist in error boundary)\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements are optional in this context\r\n\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 378, "size": 538}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary provides semantic structure\r\n        expect(errorBoundary.length).toBeGreaterThan(0);\r\n        expect(screen.getByRole('heading', { name: /something went wrong/i }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 392, "size": 239}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test for H1 elements\r\n        const headings = screen.getAllByRole('heading');\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements may not exist in all page states\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 401, "size": 377}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 1, "blocks": [{"type": "pattern", "content": "if (errorBoundary.length > 0) {\r\n        // Error scenario - error boundary has H3 heading\r\n        const headings = screen.getAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 409, "size": 210}, {"type": "pattern", "content": "if (errorBoundary.length > 0) {\n      // Error scenario - error boundary caught an error\n      expect(errorBoundary.length).toBeGreaterThan(0);\n      return { success: false, hasError: true }", "normalized": "if (errorBoundary.length > NUMBER) {", "hash": "84a95d0135fb8d78d9fcd45050232de6", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 354, "size": 191}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.9836065573770492, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n          console.log(`📊 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 ${pageConfig.name}", "hash": "7639b0889c1c69d3a4fba105098676fd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 212, "size": 74}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 88, "size": 74}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 0.9761904761904762, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\carousel.tsx", "line": 38, "size": 91}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error('useLanguage must be used within MockLanguageProvider');\r\n  }", "normalized": "if (!context) { throw new Error(STRING); }", "hash": "60573700a7dae316a4588157a73629e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.integration.test.tsx", "line": 74, "size": 98}], "files": ["components\\ui\\carousel.tsx", "tests\\integration\\contexts\\LanguageContext.integration.test.tsx"]}, {"similarity": 0.9761904761904762, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\chart.tsx", "line": 31, "size": 94}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error('useLanguage must be used within MockLanguageProvider');\r\n  }", "normalized": "if (!context) { throw new Error(STRING); }", "hash": "60573700a7dae316a4588157a73629e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.integration.test.tsx", "line": 74, "size": 98}], "files": ["components\\ui\\chart.tsx", "tests\\integration\\contexts\\LanguageContext.integration.test.tsx"]}, {"similarity": 0.9613259668508287, "blocks": [{"type": "pattern", "content": "switch (severity) {\r\n      case 'normal': return 'green';\r\n      case 'mild': return 'blue';\r\n      case 'moderate': return 'yellow';\r\n      case 'severe': return 'orange';\r\n      case 'critical': return 'red';\r\n      default: return 'gray';\r\n    }", "normalized": "switch (severity) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "6320d212187664ba1e00919bbcd9886c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\medical-conditions\\DegenerationProcess.tsx", "line": 24, "size": 248}, {"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'py-8';\r\n      case 'md':\r\n        return 'py-12';\r\n      case 'lg':\r\n        return 'py-16';\r\n      case 'xl':\r\n        return 'py-20';\r\n      default:\r\n        return 'py-16';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "3a742d790a7284dd97a6caabe219d0f1", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\SectionContainer.tsx", "line": 38, "size": 277}], "files": ["components\\medical-conditions\\DegenerationProcess.tsx", "components\\shared\\SectionContainer.tsx"]}, {"similarity": 0.9483870967741935, "blocks": [{"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'p-4';\r\n      case 'lg':\r\n        return 'p-8';\r\n      case 'xl':\r\n        return 'p-12';\r\n      default:\r\n        return 'p-6';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "172735f14d610dde73227c59cb906a91", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\GlassCard.tsx", "line": 36, "size": 228}, {"type": "pattern", "content": "switch (background) {\r\n      case 'muted':\r\n        return 'bg-muted/30';\r\n      case 'primary':\r\n        return 'bg-primary/5';\r\n      case 'secondary':\r\n        return 'bg-secondary/5';\r\n      case 'accent':\r\n        return 'bg-accent/5';\r\n      default:\r\n        return 'bg-background';\r\n    }", "normalized": "switch (background) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "2a2b92f776d3492aaac3a15c66b5c5e3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\SectionContainer.tsx", "line": 23, "size": 296}], "files": ["components\\shared\\GlassCard.tsx", "components\\shared\\SectionContainer.tsx"]}, {"similarity": 0.9327731092436975, "blocks": [{"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n          if (import.meta.env.DEV) {\r\n            console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 118, "size": 138}, {"type": "pattern", "content": "if (results.allErrors.length > 0) {\n      if (import.meta.env.DEV) {\n        console.warn(`⚠️ Errors found in ${pageName}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageName}", "hash": "8d995204e813ad2cc4b39462b49ebc2b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 320, "size": 121}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.9327731092436975, "blocks": [{"type": "pattern", "content": "if (results.allErrors.length > 0) {\r\n            if (import.meta.env.DEV) {\r\n              console.warn(`⚠️ Errors found in ${pageConfig.name}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageConfig.name}", "hash": "2a08772ef3926aa17339d5607514cc60", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 102, "size": 142}, {"type": "pattern", "content": "if (results.allErrors.length > 0) {\n      if (import.meta.env.DEV) {\n        console.warn(`⚠️ Errors found in ${pageName}", "normalized": "if (results.allErrors.length > NUMBER) { if (import.meta.env.DEV) { console.warn(`⚠️ Errors found in ${pageName}", "hash": "8d995204e813ad2cc4b39462b49ebc2b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 320, "size": 121}], "files": ["tests\\utils\\batch-page-tester.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\network\\NetworkUtils.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\index.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\network\\NetworkUtils.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\index.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\network\\NetworkUtils.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\index.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\index.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\index.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\index.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\index.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9310344827586207, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9298245614035088, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for appointment-related content\r\n          const appointmentContent = screen.queryAllByText(/appointment/i);\r\n          const bookContent = screen.queryAllByText(/book/i);\r\n          const scheduleContent = screen.queryAllByText(/schedule/i);\r\n\r\n          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 120, "size": 482}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n          // Success scenario - check for contact-related content\r\n          const contactContent = screen.queryAllByText(/contact/i);\r\n          const formContent = screen.queryAllByText(/form/i);\r\n          const phoneContent = screen.queryAllByText(/phone/i);\r\n          \r\n          const totalContent = contactContent.length + formContent.length + phoneContent.length;\r\n          expect(totalContent).toBeGreaterThan(0);\r\n        }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 111, "size": 467}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test responsive elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test mobile-specific elements (handle multiple menu buttons)\r\n        const mobileNavElement = screen.queryByTestId('mobile-navigation');\r\n        const menuButtons = screen.queryAllByRole('button', { name: /menu/i }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 208, "size": 374}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test device-specific elements\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 284, "size": 157}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - component rendered correctly\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 48, "size": 156}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test semantic structure\r\n        expect(mainElements.length).toBeGreaterThan(0);\r\n\r\n        // Test for headings\r\n        const headings = screen.queryAllByRole('heading');\r\n        expect(headings.length).toBeGreaterThan(0);\r\n\r\n        // Test for H1 elements (may not exist in error boundary)\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements are optional in this context\r\n\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 378, "size": 538}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (mainElements.length > 0) {\r\n        // Success scenario - test for H1 elements\r\n        const headings = screen.getAllByRole('heading');\r\n        const _h1Elements = headings.filter(heading =>\r\n          heading.tagName.toLowerCase() === 'h1'\r\n        );\r\n        // H1 elements may not exist in all page states\r\n        expect(headings.length).toBeGreaterThan(0);\r\n      }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 401, "size": 377}, {"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\enhanced-test-helpers.tsx"]}, {"similarity": 0.9142857142857143, "blocks": [{"type": "pattern", "content": "if (navElements.length > 0) {\r\n        // Navigation is present and functional\r\n      }", "normalized": "if (navElements.length > NUMBER) {", "hash": "6f2dd913f78828159492c7baaa332cdf", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\enhanced-test-helpers.tsx", "line": 265, "size": 87}, {"type": "pattern", "content": "if (mainElements.length > 0) {\n      // Success scenario - component rendered correctly\n      expect(mainElements.length).toBeGreaterThan(0);\n      return { success: true, hasError: false }", "normalized": "if (mainElements.length > NUMBER) {", "hash": "0fe41354a58534d67b041cfa2e9a9be4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 350, "size": 189}], "files": ["tests\\utils\\enhanced-test-helpers.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.9069767441860465, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Appointments page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Appointments page render VAR: ${renderTime}", "hash": "55b9bcc0c60cfaa70ec6a23f7ba8e2a8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 276, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Contact page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Contact page render VAR: ${renderTime}", "hash": "553ff50624e56cca4e3affa60b2ec694", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 237, "size": 91}], "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 0.9032258064516129, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 105, "size": 72}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 138, "size": 77}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 0.9032258064516129, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 151, "size": 75}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 88, "size": 74}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 0.9032258064516129, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n          console.log(`📊 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 ${pageConfig.name}", "hash": "7639b0889c1c69d3a4fba105098676fd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 212, "size": 74}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error(`❌ ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.error(`❌ ${pageConfig.name}", "hash": "fe181ec92fa80c04c50f318c14e07016", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 138, "size": 77}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\batch-page-tester.tsx"]}, {"similarity": 0.9, "blocks": [{"type": "pattern", "content": "if (error.status && error.status < 500) {\r\n      return false;\r\n    }", "normalized": "if (error.status && error.status < NUMBER) { return false; }", "hash": "3a2706d5e6a27e2e68cf569243f202a7", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 176, "size": 69}, {"type": "pattern", "content": "if (error.status && error.status >= 500) {\r\n      return true;\r\n    }", "normalized": "if (error.status && error.status >= NUMBER) { return true; }", "hash": "4893516ac5d097497dcd9f8403b0c7bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 131, "size": 69}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "services\\api\\network\\NetworkUtils.ts"]}, {"similarity": 0.8962264150943396, "blocks": [{"type": "pattern", "content": "switch (backgroundVariant) {\r\n      case 'muted':\r\n        return 'bg-muted/30';\r\n      case 'primary':\r\n        return 'bg-primary/5';\r\n      default:\r\n        return 'bg-background';\r\n    }", "normalized": "switch (backgroundVariant) { case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "c8cfe2c31eb59963c7569fc703a7fff3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\patient-resources\\ContentSection.tsx", "line": 19, "size": 191}, {"type": "pattern", "content": "switch (variant) {\r\n      case 'subtle':\r\n        return 'bg-card/30 backdrop-blur-sm border border-border/30';\r\n      case 'strong':\r\n        return 'bg-card/80 backdrop-blur-md border border-border/60';\r\n      default:\r\n        return 'bg-card/50 backdrop-blur-sm border border-border/50';\r\n    }", "normalized": "switch (variant) { case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "f95d43892de9b4277cec2736be6a385e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\GlassCard.tsx", "line": 25, "size": 298}], "files": ["components\\patient-resources\\ContentSection.tsx", "components\\shared\\GlassCard.tsx"]}, {"similarity": 0.8813559322033898, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8813559322033898, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["services\\index.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8813559322033898, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8813559322033898, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8793103448275862, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8793103448275862, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8793103448275862, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8793103448275862, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\utils\\standard-mocks.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.875, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8695652173913043, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\carousel.tsx", "line": 38, "size": 91}, {"type": "pattern", "content": "if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }", "normalized": "if (!fieldContext) { throw new Error(STRING) }", "hash": "19d312d80e7c1dcaa7e8f68aefee047b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\form.tsx", "line": 52, "size": 96}], "files": ["components\\ui\\carousel.tsx", "components\\ui\\form.tsx"]}, {"similarity": 0.8695652173913043, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\chart.tsx", "line": 31, "size": 94}, {"type": "pattern", "content": "if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }", "normalized": "if (!fieldContext) { throw new Error(STRING) }", "hash": "19d312d80e7c1dcaa7e8f68aefee047b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\form.tsx", "line": 52, "size": 96}], "files": ["components\\ui\\chart.tsx", "components\\ui\\form.tsx"]}, {"similarity": 0.8688524590163934, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 105, "size": 72}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n      console.log(`🔍 ${pageName}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageName}", "hash": "c22a27a7c5484e8d0bb9e2d8ddbab4c4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 307, "size": 60}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8688524590163934, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log(`🔍 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageConfig.name}", "hash": "d9106790761b3d5f8afdefff70b19999", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\batch-page-tester.tsx", "line": 88, "size": 74}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n      console.log(`🔍 ${pageName}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageName}", "hash": "c22a27a7c5484e8d0bb9e2d8ddbab4c4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 307, "size": 60}], "files": ["tests\\utils\\batch-page-tester.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8657718120805369, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n          console.log('📊 Navigation Test Results:', {\r\n          navigationElements: navigationElements.length,\r\n          errorBoundary: errorBoundary.length,\r\n          totalContent: anyContent.length,\r\n          hasValidContent\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, { VAR: navigationElements.length, VAR: errorBoundary.length, VAR: anyContent.length, hasValidContent }", "hash": "6ca2c8a64e2ae92035ed1712d3297c12", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 104, "size": 272}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n          console.log('📊 Language Component Test Results:', {\r\n          languageComponent: !!languageComponent,\r\n          errorBoundary: errorBoundary.length,\r\n          totalContent: anyContent.length,\r\n          hasValidContent\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, { VAR: !!languageComponent, VAR: errorBoundary.length, VAR: anyContent.length, hasValidContent }", "hash": "53919e76ef5198f2d2d4924cd578065c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 92, "size": 273}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8656716417910447, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "services\\api\\network\\NetworkUtils.ts"]}, {"similarity": 0.8656716417910447, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "services\\index.ts"]}, {"similarity": 0.8656716417910447, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8656716417910447, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8641975308641975, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Navigation render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Navigation render VAR: ${renderTime}", "hash": "21accb6f34e965db2d26363a72225141", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 265, "size": 91}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Contact page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Contact page render VAR: ${renderTime}", "hash": "553ff50624e56cca4e3affa60b2ec694", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 237, "size": 91}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 0.8636363636363636, "blocks": [{"type": "pattern", "content": "if (typeof window === 'undefined') {\r\n        return;\r\n      }", "normalized": "if (typeof VAR === STRING) { return; }", "hash": "6a9a9d817ed93e21b539c7d5c798f0f5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\security.ts", "line": 58, "size": 62}, {"type": "pattern", "content": "if (typeof error === 'string') {\r\n    return error;\r\n  }", "normalized": "if (typeof VAR === STRING) { return error; }", "hash": "c0d82a3bed40a697170dbba3f41c885c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\type-safety.ts", "line": 175, "size": 56}], "files": ["lib\\security.ts", "lib\\type-safety.ts"]}, {"similarity": 0.86, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.log(message);\n  }", "normalized": "if (import.meta.env.DEV) { console.log(message); }", "hash": "e9c7b3dc0b13ce189bd102fa5af0b9d4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 10, "size": 56}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.86, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.log(message);\n  }", "normalized": "if (import.meta.env.DEV) { console.log(message); }", "hash": "e9c7b3dc0b13ce189bd102fa5af0b9d4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 10, "size": 56}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.86, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.log(message);\n  }", "normalized": "if (import.meta.env.DEV) { console.log(message); }", "hash": "e9c7b3dc0b13ce189bd102fa5af0b9d4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 10, "size": 56}], "files": ["tests\\integration\\runIntegrationTests.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8571428571428571, "blocks": [{"type": "pattern", "content": "if (buttons.length > 0) {\r\n          fireEvent.click(buttons[0]);\r\n          expect(buttons[0]).toBeInTheDocument();\r\n        }", "normalized": "if (buttons.length > NUMBER) { fireEvent.click(buttons[NUMBER]); expect(buttons[NUMBER]).toBeInTheDocument(); }", "hash": "931adaa5b3b94ba72f64034b8978059b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 150, "size": 127}, {"type": "pattern", "content": "if (allButtons.length > 0) {\r\n          try {\r\n            fireEvent.click(allButtons[0]);\r\n            expect(allButtons[0]).toBeInTheDocument();\r\n          }", "normalized": "if (allButtons.length > NUMBER) { try { fireEvent.click(allButtons[NUMBER]); expect(allButtons[NUMBER]).toBeInTheDocument(); }", "hash": "dd8cce6ca936d037c2873eac7bf3d164", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 234, "size": 159}], "files": ["tests\\integration\\components\\Navigation.integration.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8571428571428571, "blocks": [{"type": "pattern", "content": "if (menuButtons.length > 0) {\r\n          fireEvent.click(menuButtons[0]);\r\n          expect(menuButtons[0]).toBeInTheDocument();\r\n        }", "normalized": "if (menuButtons.length > NUMBER) { fireEvent.click(menuButtons[NUMBER]); expect(menuButtons[NUMBER]).toBeInTheDocument(); }", "hash": "7626e389698d5502b28231d414445f25", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.integration.test.tsx", "line": 224, "size": 139}, {"type": "pattern", "content": "if (allButtons.length > 0) {\r\n          try {\r\n            fireEvent.click(allButtons[0]);\r\n            expect(allButtons[0]).toBeInTheDocument();\r\n          }", "normalized": "if (allButtons.length > NUMBER) { try { fireEvent.click(allButtons[NUMBER]); expect(allButtons[NUMBER]).toBeInTheDocument(); }", "hash": "dd8cce6ca936d037c2873eac7bf3d164", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 234, "size": 159}], "files": ["tests\\integration\\components\\Navigation.integration.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8524590163934426, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n          console.log(`📊 ${pageConfig.name}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 ${pageConfig.name}", "hash": "7639b0889c1c69d3a4fba105098676fd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 212, "size": 74}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n      console.log(`🔍 ${pageName}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageName}", "hash": "c22a27a7c5484e8d0bb9e2d8ddbab4c4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 307, "size": 60}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8505747126436781, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Language component render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Language component render VAR: ${renderTime}", "hash": "05d2e7667b8f9d46c65f17fb80be2769", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 296, "size": 99}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Contact page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Contact page render VAR: ${renderTime}", "hash": "553ff50624e56cca4e3affa60b2ec694", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\ContactPage.integration.test.tsx", "line": 237, "size": 91}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx"]}, {"similarity": 0.8484848484848485, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('📊 MOST COMMON ERROR TYPES:', errorTypeCounts);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorTypeCounts); }", "hash": "1d260cd3785af6ead3274ee84ce9c557", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 289, "size": 109}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8484848484848485, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('📊 MOST COMMON ERROR TYPES:', errorTypeCounts);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorTypeCounts); }", "hash": "1d260cd3785af6ead3274ee84ce9c557", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 289, "size": 109}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8484848484848485, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('📊 MOST COMMON ERROR TYPES:', errorTypeCounts);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorTypeCounts); }", "hash": "1d260cd3785af6ead3274ee84ce9c557", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 289, "size": 109}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8478260869565217, "blocks": [{"type": "pattern", "content": "if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }", "normalized": "if (!fieldContext) { throw new Error(STRING) }", "hash": "19d312d80e7c1dcaa7e8f68aefee047b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\form.tsx", "line": 52, "size": 96}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error('useLanguage must be used within MockLanguageProvider');\r\n  }", "normalized": "if (!context) { throw new Error(STRING); }", "hash": "60573700a7dae316a4588157a73629e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.integration.test.tsx", "line": 74, "size": 98}], "files": ["components\\ui\\form.tsx", "tests\\integration\\contexts\\LanguageContext.integration.test.tsx"]}, {"similarity": 0.8444444444444444, "blocks": [{"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'p-4';\r\n      case 'lg':\r\n        return 'p-8';\r\n      case 'xl':\r\n        return 'p-12';\r\n      default:\r\n        return 'p-6';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "172735f14d610dde73227c59cb906a91", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\GlassCard.tsx", "line": 36, "size": 228}, {"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'py-8';\r\n      case 'md':\r\n        return 'py-12';\r\n      case 'lg':\r\n        return 'py-16';\r\n      case 'xl':\r\n        return 'py-20';\r\n      default:\r\n        return 'py-16';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "3a742d790a7284dd97a6caabe219d0f1", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\SectionContainer.tsx", "line": 38, "size": 277}], "files": ["components\\shared\\GlassCard.tsx", "components\\shared\\SectionContainer.tsx"]}, {"similarity": 0.84375, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Hero Section Present:', !!heroSection);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, !!heroSection); }", "hash": "8a25a27521c5add92852090fd8477076", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 98, "size": 97}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.84375, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Hero Section Present:', !!heroSection);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, !!heroSection); }", "hash": "8a25a27521c5add92852090fd8477076", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 98, "size": 97}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8431372549019608, "blocks": [{"type": "pattern", "content": "if (header) {\r\n        expect(header).toBeInTheDocument();\r\n      }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 141, "size": 67}, {"type": "pattern", "content": "if (footer) {\n      expect(footer).toBeInTheDocument();\n    }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 379, "size": 61}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8431372549019608, "blocks": [{"type": "pattern", "content": "if (footer) {\r\n        expect(footer).toBeInTheDocument();\r\n      }", "normalized": "if (footer) { expect(footer).toBeInTheDocument(); }", "hash": "9d7d8b3c06428f0ab2ab5621db977ae2", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\HomePage.integration.test.tsx", "line": 148, "size": 67}, {"type": "pattern", "content": "if (header) {\n      expect(header).toBeInTheDocument();\n    }", "normalized": "if (header) { expect(header).toBeInTheDocument(); }", "hash": "df324d17579e9969a45e669eec48f0be", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 372, "size": 61}], "files": ["tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8390804597701149, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Navigation render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Navigation render VAR: ${renderTime}", "hash": "21accb6f34e965db2d26363a72225141", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 265, "size": 91}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Language component render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Language component render VAR: ${renderTime}", "hash": "05d2e7667b8f9d46c65f17fb80be2769", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 296, "size": 99}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8289473684210527, "blocks": [{"type": "pattern", "content": "switch (columns) {\r\n      case 1:\r\n        return 'grid-cols-1';\r\n      case 2:\r\n        return 'grid-cols-1 md:grid-cols-2';\r\n      case 3:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n      case 4:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';\r\n      default:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n    }", "normalized": "switch (columns) { case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; VAR: return STRING; }", "hash": "f7b9ed4f2f74ca15a868aa67aceddf7c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\patient-resources\\CardGrid.tsx", "line": 34, "size": 361}, {"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'p-4';\r\n      case 'lg':\r\n        return 'p-8';\r\n      case 'xl':\r\n        return 'p-12';\r\n      default:\r\n        return 'p-6';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "172735f14d610dde73227c59cb906a91", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\GlassCard.tsx", "line": 36, "size": 228}], "files": ["components\\patient-resources\\CardGrid.tsx", "components\\shared\\GlassCard.tsx"]}, {"similarity": 0.828125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Hero Section Present:', !!heroSection);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, !!heroSection); }", "hash": "8a25a27521c5add92852090fd8477076", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 98, "size": 97}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8260869565217391, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log('Service Worker registered successfully:', registration.scope);\r\n          }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, registration.scope); }", "hash": "b5771bacd0724ff76987a154bd8b41db", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\mobile-optimization.ts", "line": 253, "size": 128}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Stack:', renderError.stack);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, renderError.stack); }", "hash": "9e3a80867b5fa29ad8535af8bb3a8f89", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 83, "size": 86}], "files": ["lib\\mobile-optimization.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8260869565217391, "blocks": [{"type": "pattern", "content": "if (!response.ok) {\r\n          throw new Error('Failed to fetch exercise data');\r\n        }", "normalized": "if (!response.ok) { throw new Error(STRING); }", "hash": "d38e6f5227cdf83efa539d0a3a949c0d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\patient-resources\\ExerciseLibrary.tsx", "line": 93, "size": 91}, {"type": "pattern", "content": "if (!context) {\r\n    throw new Error('useLanguage must be used within MockLanguageProvider');\r\n  }", "normalized": "if (!context) { throw new Error(STRING); }", "hash": "60573700a7dae316a4588157a73629e5", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.integration.test.tsx", "line": 74, "size": 98}], "files": ["pages\\patient-resources\\ExerciseLibrary.tsx", "tests\\integration\\contexts\\LanguageContext.integration.test.tsx"]}, {"similarity": 0.8255813953488372, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Navigation render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Navigation render VAR: ${renderTime}", "hash": "21accb6f34e965db2d26363a72225141", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 265, "size": 91}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Appointments page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Appointments page render VAR: ${renderTime}", "hash": "55b9bcc0c60cfaa70ec6a23f7ba8e2a8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 276, "size": 96}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\AppointmentsPage.integration.test.tsx"]}, {"similarity": 0.8235294117647058, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Stack:', renderError.stack);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, renderError.stack); }", "hash": "9e3a80867b5fa29ad8535af8bb3a8f89", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 83, "size": 86}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8235294117647058, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Stack:', renderError.stack);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, renderError.stack); }", "hash": "9e3a80867b5fa29ad8535af8bb3a8f89", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 83, "size": 86}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8214285714285714, "blocks": [{"type": "pattern", "content": "switch (columns) {\r\n      case 2:\r\n        return 'grid-cols-1 md:grid-cols-2';\r\n      case 3:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n      case 4:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';\r\n      default:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n    }", "normalized": "switch (columns) { case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; VAR: return STRING; }", "hash": "b433cf83655feb0614cc01c770dcb33e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\core\\FeatureGrid.tsx", "line": 27, "size": 315}, {"type": "pattern", "content": "switch (columns) {\r\n      case 1:\r\n        return 'grid-cols-1';\r\n      case 2:\r\n        return 'grid-cols-1 md:grid-cols-2';\r\n      case 3:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n      case 4:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';\r\n      default:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n    }", "normalized": "switch (columns) { case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; VAR: return STRING; }", "hash": "f7b9ed4f2f74ca15a868aa67aceddf7c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\patient-resources\\CardGrid.tsx", "line": 34, "size": 361}], "files": ["components\\core\\FeatureGrid.tsx", "components\\patient-resources\\CardGrid.tsx"]}, {"similarity": 0.8193548387096774, "blocks": [{"type": "pattern", "content": "switch (columns) {\r\n      case 1:\r\n        return 'grid-cols-1';\r\n      case 2:\r\n        return 'grid-cols-1 md:grid-cols-2';\r\n      case 3:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n      case 4:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';\r\n      default:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n    }", "normalized": "switch (columns) { case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; VAR: return STRING; }", "hash": "f7b9ed4f2f74ca15a868aa67aceddf7c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\patient-resources\\CardGrid.tsx", "line": 34, "size": 361}, {"type": "pattern", "content": "switch (background) {\r\n      case 'muted':\r\n        return 'bg-muted/30';\r\n      case 'primary':\r\n        return 'bg-primary/5';\r\n      case 'secondary':\r\n        return 'bg-secondary/5';\r\n      case 'accent':\r\n        return 'bg-accent/5';\r\n      default:\r\n        return 'bg-background';\r\n    }", "normalized": "switch (background) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "2a2b92f776d3492aaac3a15c66b5c5e3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\SectionContainer.tsx", "line": 23, "size": 296}], "files": ["components\\patient-resources\\CardGrid.tsx", "components\\shared\\SectionContainer.tsx"]}, {"similarity": 0.8181818181818182, "blocks": [{"type": "pattern", "content": "switch (columns) {\r\n      case 2:\r\n        return 'grid-cols-1 md:grid-cols-2';\r\n      case 3:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n      case 4:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';\r\n      default:\r\n        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';\r\n    }", "normalized": "switch (columns) { case VAR: return STRING; case VAR: return STRING; case VAR: return STRING; VAR: return STRING; }", "hash": "b433cf83655feb0614cc01c770dcb33e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\core\\FeatureGrid.tsx", "line": 27, "size": 315}, {"type": "pattern", "content": "switch (size) {\r\n      case 'sm':\r\n        return 'py-8';\r\n      case 'lg':\r\n        return 'py-20';\r\n      case 'xl':\r\n        return 'py-24';\r\n      default:\r\n        return 'py-16';\r\n    }", "normalized": "switch (size) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "c5ee77aef658d96a5164ec0a113ce440", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\layout-utils.ts", "line": 118, "size": 191}], "files": ["components\\core\\FeatureGrid.tsx", "lib\\layout-utils.ts"]}, {"similarity": 0.8160919540229885, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log(`📊 Language component render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Language component render VAR: ${renderTime}", "hash": "05d2e7667b8f9d46c65f17fb80be2769", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 296, "size": 99}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log(`📊 Appointments page render time: ${renderTime}", "normalized": "if (import.meta.env.DEV) { console.log(`📊 Appointments page render VAR: ${renderTime}", "hash": "55b9bcc0c60cfaa70ec6a23f7ba8e2a8", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "line": 276, "size": 96}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\AppointmentsPage.integration.test.tsx"]}, {"similarity": 0.8142857142857143, "blocks": [{"type": "pattern", "content": "switch (field.type) {\r\n      case 'select':\r\n        return (\r\n          <select\r\n            {...baseProps}", "normalized": "switch (field.type) { case STRING: return ( <select {...baseProps}", "hash": "94a7c15048ffbda894d334324c5dd9cb", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\consulting-rooms\\InquiryForm.tsx", "line": 33, "size": 108}, {"type": "pattern", "content": "switch (field.type) {\r\n      case 'textarea':\r\n        return (\r\n          <Textarea\r\n            {...commonProps}", "normalized": "switch (field.type) { case STRING: return ( <Textarea {...commonProps}", "hash": "3b64baf0c79a795d419e116f320792a9", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\core\\ContactForm.tsx", "line": 65, "size": 114}], "files": ["components\\consulting-rooms\\InquiryForm.tsx", "components\\core\\ContactForm.tsx"]}, {"similarity": 0.8142857142857143, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n            console.log('Service Worker registered successfully:', registration.scope);\r\n          }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, registration.scope); }", "hash": "b5771bacd0724ff76987a154bd8b41db", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\mobile-optimization.ts", "line": 253, "size": 128}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Render Error:', renderError.message);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, renderError.message); }", "hash": "54a2559a64e8924fcdfc1c40b5f5d7f9", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 80, "size": 95}], "files": ["lib\\mobile-optimization.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n    console.log(`🪝 [HOOK:${hookName}", "normalized": "if (import.meta.env.DEV) { console.log(`🪝 [VAR:${hookName}", "hash": "2645b9d83b8c7f4f66252ebc4b391d5a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\lib\\dev-console.ts", "line": 91, "size": 65}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n      console.log(`🔍 ${pageName}", "normalized": "if (import.meta.env.DEV) { console.log(`🔍 ${pageName}", "hash": "c22a27a7c5484e8d0bb9e2d8ddbab4c4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\integration-test-setup.ts", "line": 307, "size": 60}], "files": ["lib\\dev-console.ts", "tests\\utils\\integration-test-setup.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8135593220338984, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.error(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.error(message, error); }", "hash": "fd578104817ab5d6338a00ce1c1d0c6b", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 28, "size": 65}], "files": ["tests\\utils\\standard-mocks.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["services\\index.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8125, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.error('Queued request failed:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\network\\NetworkUtils.ts", "line": 337, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["services\\api\\network\\NetworkUtils.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}], "files": ["services\\index.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["services\\index.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["services\\index.ts", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.error('API Error:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\index.ts", "line": 85, "size": 80}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["services\\index.ts", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Navigation rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 117, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('❌ Language component rendering failed:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, error); }", "hash": "40b8223313832bae8c3df5e57893f9ca", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 105, "size": 112}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8103448275862069, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\n    console.warn(message, error);\n  }", "normalized": "if (import.meta.env.DEV) { console.warn(message, error); }", "hash": "3cc1f8649192e7f53ce297aeed439530", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\test-logging.ts", "line": 19, "size": 64}], "files": ["tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "tests\\utils\\test-logging.ts"]}, {"similarity": 0.8088235294117647, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Stack:', renderError.stack);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, renderError.stack); }", "hash": "9e3a80867b5fa29ad8535af8bb3a8f89", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 83, "size": 86}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('🔍 SYSTEMATIC ERROR PATTERNS:', errorPatterns);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorPatterns); }", "hash": "7c06277bd82f51b6f0916a1a8da8a6bc", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 278, "size": 109}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}], "files": ["services\\api\\cache\\CacheManager.ts", "tests\\integration\\runIntegrationTests.ts"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Component threw error during render');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 106, "size": 96}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('🎉 ALL CORE PAGES TESTED SUCCESSFULLY WITH NO ERRORS!');\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 308, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\pages\\CorePages.batch.test.tsx", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.8070175438596491, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n      console.log('🚀 Starting comprehensive integration test execution...\\n');\r\n    }", "normalized": "if (import.meta.env.DEV) { console.log(STRING); }", "hash": "c566a79b729bf8098688667c6dba0ecd", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\runIntegrationTests.ts", "line": 187, "size": 114}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["tests\\integration\\runIntegrationTests.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.8066298342541437, "blocks": [{"type": "pattern", "content": "switch (severity) {\r\n      case 'normal': return 'green';\r\n      case 'mild': return 'blue';\r\n      case 'moderate': return 'yellow';\r\n      case 'severe': return 'orange';\r\n      case 'critical': return 'red';\r\n      default: return 'gray';\r\n    }", "normalized": "switch (severity) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "6320d212187664ba1e00919bbcd9886c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\medical-conditions\\DegenerationProcess.tsx", "line": 24, "size": 248}, {"type": "pattern", "content": "switch (padding) {\r\n      case 'none':\r\n        return '';\r\n      case 'sm':\r\n        return 'p-4';\r\n      case 'lg':\r\n        return 'p-8';\r\n      case 'xl':\r\n        return 'p-12';\r\n      default:\r\n        return 'p-6';\r\n    }", "normalized": "switch (padding) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "172735f14d610dde73227c59cb906a91", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\GlassCard.tsx", "line": 36, "size": 228}], "files": ["components\\medical-conditions\\DegenerationProcess.tsx", "components\\shared\\GlassCard.tsx"]}, {"similarity": 0.8066298342541437, "blocks": [{"type": "pattern", "content": "switch (severity) {\r\n      case 'normal': return 'green';\r\n      case 'mild': return 'blue';\r\n      case 'moderate': return 'yellow';\r\n      case 'severe': return 'orange';\r\n      case 'critical': return 'red';\r\n      default: return 'gray';\r\n    }", "normalized": "switch (severity) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "6320d212187664ba1e00919bbcd9886c", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\medical-conditions\\DegenerationProcess.tsx", "line": 24, "size": 248}, {"type": "pattern", "content": "switch (background) {\r\n      case 'muted':\r\n        return 'bg-muted/30';\r\n      case 'primary':\r\n        return 'bg-primary/5';\r\n      case 'secondary':\r\n        return 'bg-secondary/5';\r\n      case 'accent':\r\n        return 'bg-accent/5';\r\n      default:\r\n        return 'bg-background';\r\n    }", "normalized": "switch (background) { case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; case STRING: return STRING; VAR: return STRING; }", "hash": "2a2b92f776d3492aaac3a15c66b5c5e3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\shared\\SectionContainer.tsx", "line": 23, "size": 296}], "files": ["components\\medical-conditions\\DegenerationProcess.tsx", "components\\shared\\SectionContainer.tsx"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to store in localStorage:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 25, "size": 101}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\errors\\ServiceErrorHandler.ts"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Failed to cleanup persistent cache:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 222, "size": 104}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\errors\\ServiceErrorHandler.ts"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.warn('Error during persistent cache cleanup:', error);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\cache\\CacheManager.ts", "line": 242, "size": 107}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}], "files": ["services\\api\\cache\\CacheManager.ts", "services\\api\\errors\\ServiceErrorHandler.ts"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.log('Component Error:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 119, "size": 88}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\debug\\IndexComponentDebug.test.tsx"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button click test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\components\\Navigation.simplified.test.tsx", "line": 239, "size": 119}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n              console.log('Button interaction test skipped due to error:', error);\r\n            }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, error); }", "hash": "246237cd25adccd60f04549e2ab7695a", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\contexts\\LanguageContext.simplified.test.tsx", "line": 177, "size": 125}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"]}, {"similarity": 0.8059701492537313, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.error('Failed to report error:', reportingError);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.error(STRING, reportingError); }", "hash": "2b3174e67f08f98d0606d4f818f18cd4", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\services\\api\\errors\\ServiceErrorHandler.ts", "line": 219, "size": 106}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n          console.warn('Timeout callback error in test:', error);\r\n        }", "normalized": "if (import.meta.env.DEV) { console.warn(STRING, error); }", "hash": "17480afe1cb8459c8a0c21dd7a94c77e", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\utils\\standard-mocks.ts", "line": 164, "size": 104}], "files": ["services\\api\\errors\\ServiceErrorHandler.ts", "tests\\utils\\standard-mocks.ts"]}, {"similarity": 0.8043478260869565, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\carousel.tsx", "line": 38, "size": 91}, {"type": "pattern", "content": "if (!response.ok) {\r\n          throw new Error('Failed to fetch exercise data');\r\n        }", "normalized": "if (!response.ok) { throw new Error(STRING); }", "hash": "d38e6f5227cdf83efa539d0a3a949c0d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\patient-resources\\ExerciseLibrary.tsx", "line": 93, "size": 91}], "files": ["components\\ui\\carousel.tsx", "pages\\patient-resources\\ExerciseLibrary.tsx"]}, {"similarity": 0.8043478260869565, "blocks": [{"type": "pattern", "content": "if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }", "normalized": "if (!context) { throw new Error(STRING) }", "hash": "239be497ba6982d39d5e741b409a6dc3", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\components\\ui\\chart.tsx", "line": 31, "size": 94}, {"type": "pattern", "content": "if (!response.ok) {\r\n          throw new Error('Failed to fetch exercise data');\r\n        }", "normalized": "if (!response.ok) { throw new Error(STRING); }", "hash": "d38e6f5227cdf83efa539d0a3a949c0d", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\pages\\patient-resources\\ExerciseLibrary.tsx", "line": 93, "size": 91}], "files": ["components\\ui\\chart.tsx", "pages\\patient-resources\\ExerciseLibrary.tsx"]}, {"similarity": 0.803030303030303, "blocks": [{"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n        console.log('Hero Section Present:', !!heroSection);\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, !!heroSection); }", "hash": "8a25a27521c5add92852090fd8477076", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\debug\\IndexComponentDebug.test.tsx", "line": 98, "size": 97}, {"type": "pattern", "content": "if (import.meta.env.DEV) {\r\n\r\n        console.log('📊 MOST COMMON ERROR TYPES:', errorTypeCounts);\r\n\r\n      }", "normalized": "if (import.meta.env.DEV) { console.log(STRING, errorTypeCounts); }", "hash": "1d260cd3785af6ead3274ee84ce9c557", "file": "C:\\Users\\<USER>\\Documents\\vas-31\\src\\tests\\integration\\pages\\CorePages.batch.test.tsx", "line": 289, "size": 109}], "files": ["tests\\debug\\IndexComponentDebug.test.tsx", "tests\\integration\\pages\\CorePages.batch.test.tsx"]}], "suggestions": [{"type": "extract-pattern", "severity": "low", "message": "4 similar code patterns found", "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "extract-pattern", "severity": "low", "message": "4 similar code patterns found", "files": ["services\\api\\network\\NetworkUtils.ts", "services\\index.ts", "tests\\integration\\components\\Navigation.simplified.test.tsx", "tests\\integration\\contexts\\LanguageContext.simplified.test.tsx"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "extract-pattern", "severity": "low", "message": "6 similar code patterns found", "files": ["services\\index.ts"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "extract-pattern", "severity": "low", "message": "6 similar code patterns found", "files": ["tests\\integration\\components\\Navigation.integration.test.tsx"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "extract-pattern", "severity": "low", "message": "12 similar code patterns found", "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "extract-pattern", "severity": "low", "message": "11 similar code patterns found", "files": ["tests\\integration\\pages\\AppointmentsPage.integration.test.tsx", "tests\\integration\\pages\\ContactPage.integration.test.tsx", "tests\\integration\\pages\\HomePage.integration.test.tsx", "tests\\utils\\integration-test-setup.ts"], "suggestion": "Consider extracting to a helper function or hook", "impact": "Could improve maintainability"}, {"type": "similar-pattern", "severity": "medium", "message": "Highly similar patterns found (100% similar)", "files": ["components\\ui\\carousel.tsx", "components\\ui\\chart.tsx"], "suggestion": "Consider refactoring to use the same implementation", "impact": "Could improve consistency and maintainability"}, {"type": "similar-pattern", "severity": "medium", "message": "Highly similar patterns found (100% similar)", "files": ["contexts\\DeviceContext.tsx", "contexts\\LanguageContext.tsx"], "suggestion": "Consider refactoring to use the same implementation", "impact": "Could improve consistency and maintainability"}, {"type": "similar-pattern", "severity": "medium", "message": "Highly similar patterns found (100% similar)", "files": ["contexts\\DeviceContext.tsx", "contexts\\LanguageContext.tsx"], "suggestion": "Consider refactoring to use the same implementation", "impact": "Could improve consistency and maintainability"}, {"type": "similar-pattern", "severity": "medium", "message": "Highly similar patterns found (100% similar)", "files": ["lib\\type-safety.ts", "types\\translations.ts"], "suggestion": "Consider refactoring to use the same implementation", "impact": "Could improve consistency and maintainability"}, {"type": "similar-pattern", "severity": "medium", "message": "Highly similar patterns found (100% similar)", "files": ["services\\api\\cache\\CacheManager.ts", "tests\\utils\\standard-mocks.ts"], "suggestion": "Consider refactoring to use the same implementation", "impact": "Could improve consistency and maintainability"}], "summary": {"totalDuplicateSize": 13995, "potentialSavings": 0}}