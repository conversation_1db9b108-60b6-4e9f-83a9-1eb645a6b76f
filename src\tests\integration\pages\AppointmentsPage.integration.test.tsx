/**
 * 🎯 COMPREHENSIVE APPOINTMENTS PAGE INTEGRATION TESTS
 *
 * Systematic testing of unified Appointments page with full error identification
 */

import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';

import Appointments from '@/pages/Appointments';
import {
  EnhancedTestWrapper,
  testPageComprehensively,
  ErrorTracker
} from '@/tests/utils/enhanced-test-helpers';
import {
  setupIntegrationTestMocks,
  cleanupIntegrationTest,
  integrationTestPatterns
} from '@/tests/utils/integration-test-setup';

// Setup all standard mocks
setupIntegrationTestMocks();

describe('Appointments Page - Comprehensive Integration Tests', () => {
  beforeEach(() => {
    cleanupIntegrationTest();
    ErrorTracker.clearErrors();
  });

  describe('🎯 Systematic Page Testing', () => {
    it('passes comprehensive page test suite', async () => {
      await integrationTestPatterns.testPageComprehensively(
        'Appointments',
        Appointments,
        {
          expectedSections: [
            'Appointment with Neurosurgeon and Spine Surgeon Melbourne Victoria',
            'Appointment Types',
            'Appointment Process',
            'Request an Appointment',
            'Diagnostic Investigations',
            'Treatment Options',
            'Consultation Fees',
            'Insurance Options',
            'Telehealth Consultations',
            'Preparing for Your Appointment',
            'Our Locations',
            'Patient Privacy and Confidentiality',
            'Contact Information'
          ],
          interactiveElements: [
            'Submit Request',
            'Contact',
            'Phone',
            'Email',
            'View on Google Maps'
          ],
          performanceThresholds: {
            renderTime: 3000, // 3 seconds
            memoryUsage: 10 * 1024 * 1024 // 10MB
          }
        }
      );
    }, 30000);
  });

  describe('🔧 Basic Rendering Tests', () => {
    it('renders without crashing', async () => {
      try {
        const { unmount } = render(
          <EnhancedTestWrapper>
            <Appointments />
          </EnhancedTestWrapper>
        );

        // Wait for component to stabilize
        await waitFor(() => {
          expect(document.body).toBeInTheDocument();
        }, { timeout: 10000 });

        unmount();
      } catch (error) {
        ErrorTracker.addError('Appointments', 'BasicRendering', error);
        throw error;
      }
    });

    it('handles error boundary gracefully', async () => {
      render(
        <EnhancedTestWrapper disableErrorBoundary={false}>
          <Appointments />
        </EnhancedTestWrapper>
      );

      // Should either render content or show error boundary
      await waitFor(() => {
        const mainContent = screen.queryAllByRole('main');
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        
        expect(mainContent.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });
  });

  describe('📋 Content Structure Tests', () => {
    it('displays page structure correctly', async () => {
      render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Test for main page structure
        const mainElements = screen.queryAllByRole('main');
        const errorBoundary = screen.queryAllByText(/something went wrong/i);

        if (mainElements.length > 0) {
          // Success scenario - check for appointment-related content
          const appointmentContent = screen.queryAllByText(/appointment/i);
          const bookContent = screen.queryAllByText(/book/i);
          const scheduleContent = screen.queryAllByText(/schedule/i);

          const totalContent = appointmentContent.length + bookContent.length + scheduleContent.length;
          expect(totalContent).toBeGreaterThan(0);
        } else if (errorBoundary.length > 0) {
          // Error scenario - error boundary is working
          expect(errorBoundary.length).toBeGreaterThan(0);
        } else {
          throw new Error('Neither main content nor error boundary found');
        }
      }, { timeout: 15000 });
    });

    it('includes navigation elements', async () => {
      render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Navigation should be present (may be multiple nav elements)
        const navElements = screen.queryAllByRole('navigation');
        
        // Should have at least one navigation element or error boundary
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        
        expect(navElements.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });
  });

  describe('🎮 Interactive Elements Tests', () => {
    it('handles interactive elements properly', async () => {
      render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Look for interactive elements
        const buttons = screen.queryAllByRole('button');
        const links = screen.queryAllByRole('link');
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        
        // Should have interactive elements or error boundary
        expect(buttons.length > 0 || links.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });

    it('handles form elements if present', async () => {
      render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Look for form elements (optional)
        const forms = screen.queryAllByRole('form');
        const textboxes = screen.queryAllByRole('textbox');
        
        // Forms are optional, just ensure no errors if present
        if (forms.length > 0 || textboxes.length > 0) {
          expect(forms.length >= 0).toBe(true);
        }
      }, { timeout: 10000 });
    });
  });

  describe('🌐 Context Integration Tests', () => {
    it('integrates with device context', async () => {
      render(
        <EnhancedTestWrapper mockDeviceType="mobile">
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Should render without device context errors
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        const mainContent = screen.queryAllByRole('main');
        
        expect(mainContent.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });

    it('integrates with language context', async () => {
      render(
        <EnhancedTestWrapper mockLanguage="en">
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Should render without language context errors
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        const mainContent = screen.queryAllByRole('main');
        
        expect(mainContent.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });
  });

  describe('♿ Accessibility Tests', () => {
    it('meets basic accessibility standards', async () => {
      const { container } = render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Basic accessibility checks
        const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const images = container.querySelectorAll('img');
        
        // Check for alt text on images
        images.forEach(img => {
          const altText = img.getAttribute('alt');
          if (altText === null) {
            if (import.meta.env.DEV) {
              console.warn('Image missing alt text:', img);
            }
          }
        });

        // Should have some heading structure
        expect(headings.length >= 0).toBe(true);
      }, { timeout: 10000 });
    });
  });

  describe('⚡ Performance Tests', () => {
    it('renders within acceptable time limits', async () => {
      const startTime = performance.now();
      
      const { unmount } = render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      }, { timeout: 10000 });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Log performance metrics
      if (import.meta.env.DEV) {
        console.log(`📊 Appointments page render time: ${renderTime}ms`);
      }

      // Should render within 10 seconds (generous for integration testing)
      expect(renderTime).toBeLessThan(10000);

      unmount();
    });
  });

  describe('🚨 Error Handling Tests', () => {
    it('handles missing data gracefully', async () => {
      // Test with potentially missing data
      render(
        <EnhancedTestWrapper>
          <Appointments />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        // Should not crash with missing data
        const errorBoundary = screen.queryAllByText(/something went wrong/i);
        const mainContent = screen.queryAllByRole('main');
        
        // Either content renders or error boundary catches issues
        expect(mainContent.length > 0 || errorBoundary.length > 0).toBe(true);
      }, { timeout: 10000 });
    });
  });

  afterAll(() => {
    // Report error summary
    const errorSummary = ErrorTracker.getErrorSummary();
    if (errorSummary.totalErrors > 0) {
      if (import.meta.env.DEV) {
        console.log('🔍 Appointments Page Error Summary:', errorSummary);
      }
    }
  });
});
