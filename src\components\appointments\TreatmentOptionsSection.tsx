import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOptionsSectionProps {
  title: string;
  description: string[];
  image: {
    src: string;
    alt: string;
  };
}

const TreatmentOptionsSection: React.FC<TreatmentOptionsSectionProps> = ({
  title,
  description,
  image
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className="py-16">
      <div className="container">
        <div className={cn(
          "grid gap-12 items-center",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"
        )}>
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src={image.src}
              alt={image.alt}
              className="w-full h-auto"
              fallbackSrc="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            />
          </div>
          
          <div>
            <h2 className="text-3xl font-bold mb-6">{title}</h2>
            
            <div className="space-y-6">
              {description.map((paragraph, index) => (
                <p key={index} className="text-muted-foreground leading-relaxed">
                  {paragraph}
                </p>
              ))}
            </div>

            {/* Treatment Approach Highlights */}
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Our Treatment Philosophy</h3>
              <div className="grid gap-4">
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Badge className="bg-green-600">1st</Badge>
                      <div>
                        <h4 className="font-semibold text-green-900">Conservative Treatment First</h4>
                        <p className="text-sm text-green-800">
                          We prioritize non-surgical approaches whenever possible
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Badge className="bg-blue-600">2nd</Badge>
                      <div>
                        <h4 className="font-semibold text-blue-900">Collaborative Decision Making</h4>
                        <p className="text-sm text-blue-800">
                          Treatment decisions are made together with you and your family
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-purple-50 border-purple-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Badge className="bg-purple-600">3rd</Badge>
                      <div>
                        <h4 className="font-semibold text-purple-900">Second Opinions Encouraged</h4>
                        <p className="text-sm text-purple-800">
                          We support and encourage seeking additional medical perspectives
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Key Benefits */}
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Why Choose Our Approach?</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span className="text-muted-foreground">Minimally invasive techniques when surgery is needed</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span className="text-muted-foreground">Comprehensive risk-benefit analysis for all options</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span className="text-muted-foreground">Peer review process to ensure optimal outcomes</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <span className="text-muted-foreground">Focus on long-term quality of life improvement</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TreatmentOptionsSection;
