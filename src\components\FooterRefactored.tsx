import React from 'react';

import FooterBottom from '@/components/footer/FooterBottom';
import FooterCompanyInfo from '@/components/footer/FooterCompanyInfo';
import FooterContact from '@/components/footer/FooterContact';
import FooterSection from '@/components/footer/FooterSection';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { getFooterData } from '@/data/footer/footerData';
import { cn } from '@/lib/utils';
import en from '@/locales/en';

/**
 * Footer Component
 *
 * Modular footer implementation using data-driven architecture
 * and reusable sub-components for maintainability
 */

const Footer: React.FC = () => {
  const { t } = useLanguage();
  const deviceInfo = useDeviceDetection();

  // Get footer configuration with translations
  const safeT = t || en;
  const footerConfig = getFooterData(safeT);

  return (
    <footer className={cn(
      "bg-card text-card-foreground border-t mobile-safe-area",
      deviceInfo.isMobile ? "pt-mobile-xl pb-mobile-lg" : "pt-16 pb-8"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className={cn(
          "mb-mobile-xl",
          deviceInfo.isMobile
            ? "grid grid-cols-1 gap-mobile-lg"
            : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
        )}>
          {/* Company Info */}
          <FooterCompanyInfo
            companyName={footerConfig.companyInfo.name}
            description={footerConfig.companyInfo.description}
            socialLinks={footerConfig.socialLinks}
          />

          {/* Footer Sections */}
          {footerConfig.sections.map((section, index) => (
            <FooterSection
              key={section.id}
              section={section}
              animationDelay={200 + (index * 100)}
            />
          ))}

          {/* Contact Information */}
          <FooterContact
            title={safeT.footer?.contact || "Contact"}
            contactInfo={footerConfig.contactInfo}
            animationDelay={400}
          />
        </div>

        {/* Footer Bottom */}
        <FooterBottom
          companyName={footerConfig.companyInfo.name}
          copyrightYear={footerConfig.copyright.year}
          copyrightText={footerConfig.copyright.text}
          legalLinks={footerConfig.legalLinks}
        />
      </div>
    </footer>
  );
};

Footer.displayName = 'Footer';

export default Footer;
