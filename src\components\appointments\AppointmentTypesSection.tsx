import React from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AppointmentType {
  id: string;
  title: string;
  duration: string;
  description: string;
  features: string[];
  fee: string;
  rebate: string;
  outOfPocket: string;
}

interface AppointmentTypesSectionProps {
  title: string;
  subtitle: string;
  types: AppointmentType[];
}

const AppointmentTypesSection: React.FC<AppointmentTypesSectionProps> = ({
  title,
  subtitle,
  types
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2"
        )}>
          {types.map((type, _index) => (
            <Card key={type.id} className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start mb-2">
                  <CardTitle className="text-xl text-primary">
                    {type.title}
                  </CardTitle>
                  <Badge variant="secondary" className="ml-2">
                    {type.duration}
                  </Badge>
                </div>
                <p className="text-muted-foreground">
                  {type.description}
                </p>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-2">Includes:</h4>
                    <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                      {type.features.map((feature, featureIndex) => (
                        <li key={featureIndex}>{feature}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Pricing */}
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>
                        <p className="font-medium">Consultation Fee</p>
                        <p className="text-lg font-bold text-primary">{type.fee}</p>
                      </div>
                      <div>
                        <p className="font-medium">Medicare Rebate</p>
                        <p className="text-green-600 font-semibold">{type.rebate}</p>
                      </div>
                      <div>
                        <p className="font-medium">Out of Pocket</p>
                        <p className="font-semibold">{type.outOfPocket}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground">
            All fees are in Australian dollars. Medicare rebates require a valid GP or specialist referral.
          </p>
        </div>
      </div>
    </section>
  );
};

export default AppointmentTypesSection;
