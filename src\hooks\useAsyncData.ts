import { useCallback, useEffect } from 'react';

import { useBaseAsyncState, type BaseAsyncOptions, type BaseAsyncState } from './useBaseAsyncState';

export type AsyncDataState = BaseAsyncState;

export interface AsyncDataOptions<T> extends BaseAsyncOptions<T> {
  validateData?: (data: T) => boolean;
  transformData?: (data: unknown) => T;
}

export interface UseAsyncDataReturn<T> {
  data: T | null;
  state: AsyncDataState;
  error: Error | null;
  isLoading: boolean;
  isEmpty: boolean;
  hasError: boolean;
  isSuccess: boolean;
  retry: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
  execute: (loadFn: () => Promise<T>) => Promise<void>;
}

/**
 * Hook for managing async data loading with comprehensive state management
 */
export function useAsyncData<T>(
  loadFunction?: () => Promise<T>,
  options: AsyncDataOptions<T> = {}
): UseAsyncDataReturn<T> {
  const { validateData, transformData, ...baseOptions } = options;

  const baseState = useBaseAsyncState<T>(baseOptions);

  // Enhanced execute function with validation and transformation
  const execute = useCallback(async (loadFn: () => Promise<T>) => {
    const enhancedLoadFn = async (): Promise<T> => {
      let result = await loadFn();

      // Transform data if transformer provided
      if (transformData && result !== null && result !== undefined) {
        result = transformData(result);
      }

      // Validate data if validator provided
      if (validateData && result !== null && result !== undefined) {
        if (!validateData(result)) {
          throw new Error('Data validation failed');
        }
      }

      return result;
    };

    await baseState.executeWithRetry(enhancedLoadFn);
  }, [validateData, transformData, baseState]);

  // Refresh function (re-execute current load function)
  const refresh = useCallback(async () => {
    if (loadFunction) {
      await execute(loadFunction);
    }
  }, [loadFunction, execute]);

  // Auto-execute on mount if loadFunction provided
  useEffect(() => {
    if (loadFunction) {
      execute(loadFunction);
    }
  }, [loadFunction, execute]);

  return {
    data: baseState.data,
    state: baseState.state,
    error: baseState.error,
    isLoading: baseState.isLoading,
    isEmpty: baseState.isEmpty,
    hasError: baseState.hasError,
    isSuccess: baseState.isSuccess,
    retry: baseState.retry,
    refresh,
    reset: baseState.reset,
    execute
  };
}
