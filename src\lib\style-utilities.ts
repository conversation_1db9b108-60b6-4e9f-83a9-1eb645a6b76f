/**
 * Style Utilities
 * Consolidates duplicate style class generation patterns across components
 */

/**
 * Size variants for components
 */
export type SizeVariant = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Color variants for components
 */
export type ColorVariant = 'primary' | 'secondary' | 'accent' | 'muted' | 'destructive';

/**
 * Alignment options
 */
export type AlignmentVariant = 'left' | 'center' | 'right';

/**
 * Shape variants
 */
export type ShapeVariant = 'circle' | 'rounded' | 'square';

/**
 * Size class mappings for different component types
 */
const SIZE_CLASSES = {
  icon: {
    xs: 'h-4 w-4',
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-20 w-20',
    '2xl': 'h-24 w-24'
  },
  text: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl'
  },
  heading: {
    xs: 'text-lg',
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl',
    xl: 'text-5xl',
    '2xl': 'text-6xl'
  },
  subtitle: {
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl',
    '2xl': 'text-3xl'
  },
  description: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl'
  },
  spacing: {
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
    '2xl': 'p-10'
  }
};

/**
 * Color variant class mappings
 */
const COLOR_CLASSES = {
  background: {
    primary: 'bg-primary/10 text-primary',
    secondary: 'bg-secondary/10 text-secondary',
    accent: 'bg-accent/10 text-accent-foreground',
    muted: 'bg-muted text-muted-foreground',
    destructive: 'bg-destructive text-destructive-foreground'
  },
  text: {
    primary: 'text-primary',
    secondary: 'text-secondary',
    accent: 'text-accent',
    muted: 'text-muted-foreground',
    destructive: 'text-destructive'
  },
  border: {
    primary: 'border-primary',
    secondary: 'border-secondary',
    accent: 'border-accent',
    muted: 'border-muted',
    destructive: 'border-destructive'
  }
};

/**
 * Alignment class mappings
 */
const ALIGNMENT_CLASSES = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right'
};

/**
 * Shape class mappings
 */
const SHAPE_CLASSES = {
  circle: 'rounded-full',
  rounded: 'rounded-lg',
  square: 'rounded-none'
};

/**
 * Get size classes for a specific component type
 */
export function getSizeClass(
  size: SizeVariant, 
  type: keyof typeof SIZE_CLASSES = 'text'
): string {
  return SIZE_CLASSES[type][size] || SIZE_CLASSES[type].md;
}

/**
 * Get color variant classes for a specific usage type
 */
export function getColorClass(
  variant: ColorVariant,
  type: keyof typeof COLOR_CLASSES = 'text'
): string {
  return COLOR_CLASSES[type][variant] || COLOR_CLASSES[type].primary;
}

/**
 * Get alignment classes
 */
export function getAlignmentClass(alignment: AlignmentVariant): string {
  return ALIGNMENT_CLASSES[alignment] || ALIGNMENT_CLASSES.center;
}

/**
 * Get shape classes
 */
export function getShapeClass(shape: ShapeVariant): string {
  return SHAPE_CLASSES[shape] || SHAPE_CLASSES.rounded;
}

/**
 * Hospital type variants
 */
export type HospitalTypeVariant = 'Primary Affiliation' | 'Affiliated Hospital' | 'Consulting Hospital' | 'default';

/**
 * Hospital type color mappings
 */
const HOSPITAL_TYPE_COLORS = {
  'Primary Affiliation': 'bg-red-100 text-red-800 border-red-200',
  'Affiliated Hospital': 'bg-blue-100 text-blue-800 border-blue-200',
  'Consulting Hospital': 'bg-green-100 text-green-800 border-green-200',
  'default': 'bg-gray-100 text-gray-800 border-gray-200'
};

/**
 * Get hospital type color classes
 */
export function getHospitalTypeColor(type: string): string {
  return HOSPITAL_TYPE_COLORS[type as HospitalTypeVariant] || HOSPITAL_TYPE_COLORS.default;
}

/**
 * Utility function to combine multiple style utilities
 */
export function combineStyleClasses(
  options: {
    size?: { value: SizeVariant; type?: keyof typeof SIZE_CLASSES };
    color?: { value: ColorVariant; type?: keyof typeof COLOR_CLASSES };
    alignment?: AlignmentVariant;
    shape?: ShapeVariant;
    additional?: string[];
  }
): string {
  const classes: string[] = [];

  if (options.size) {
    classes.push(getSizeClass(options.size.value, options.size.type));
  }

  if (options.color) {
    classes.push(getColorClass(options.color.value, options.color.type));
  }

  if (options.alignment) {
    classes.push(getAlignmentClass(options.alignment));
  }

  if (options.shape) {
    classes.push(getShapeClass(options.shape));
  }

  if (options.additional) {
    classes.push(...options.additional);
  }

  return classes.filter(Boolean).join(' ');
}
