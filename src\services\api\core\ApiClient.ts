import { ServiceErrorHandler } from '../errors/ServiceErrorHandler';
import { NetworkUtils } from '../network/NetworkUtils';
import { 
  ApiConfig, 
  RequestConfig, 
  ApiResponse, 
  ServiceError, 
  RequestInterceptor, 
  ResponseInterceptor,
  RetryOptions 
} from '../types';

/**
 * Centralized API client for all HTTP requests
 */
export class ApiClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private defaultHeaders: Record<string, string>;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorHandler: ServiceErrorHandler;

  constructor(config: ApiConfig) {
    this.baseURL = config.baseURL.replace(/\/$/, ''); // Remove trailing slash
    this.timeout = config.timeout || 30000;
    this.retryAttempts = config.retryAttempts || 3;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers
    };
    this.errorHandler = ServiceErrorHandler.getInstance();
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Make HTTP request with full configuration
   */
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    // Apply request interceptors
    let requestConfig = await this.applyRequestInterceptors(config);
    
    // Add default configuration
    requestConfig = this.mergeConfig(requestConfig);

    // Execute request with retry logic
    const retryOptions: RetryOptions = {
      maxAttempts: requestConfig.retryAttempts || this.retryAttempts,
      baseDelay: 1000,
      backoffFactor: 2,
      retryCondition: (error: ServiceError) => this.isRetryableError(error)
    };

    try {
      const response = await NetworkUtils.withRetry(
        () => this.executeRequest<T>(requestConfig),
        retryOptions
      );

      // Apply response interceptors
      return await this.applyResponseInterceptors(response);
    } catch (error) {
      // Handle and transform error
      const serviceError = await this.transformError(error, requestConfig);
      await this.errorHandler.handleError(serviceError);
      throw serviceError;
    }
  }

  /**
   * GET request
   */
  async get<T>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    const response = await this.request<T>({
      ...config,
      method: 'GET',
      url
    });
    return response.data;
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<T> {
    const response = await this.request<T>({
      ...config,
      method: 'POST',
      url,
      data
    });
    return response.data;
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<T> {
    const response = await this.request<T>({
      ...config,
      method: 'PUT',
      url,
      data
    });
    return response.data;
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    const response = await this.request<T>({
      ...config,
      method: 'DELETE',
      url
    });
    return response.data;
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<T> {
    const response = await this.request<T>({
      ...config,
      method: 'PATCH',
      url,
      data
    });
    return response.data;
  }

  /**
   * Execute the actual HTTP request
   */
  private async executeRequest<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const url = this.buildUrl(config.url);
    const controller = new AbortController();
    const signal = config.signal || controller.signal;

    // Set up timeout
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.timeout);

    try {
      const response = await fetch(url, {
        method: config.method || 'GET',
        headers: config.headers,
        body: config.data ? JSON.stringify(config.data) : undefined,
        signal
      });

      clearTimeout(timeoutId);

      // Parse response
      const responseData = await this.parseResponse<T>(response);

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: this.parseHeaders(response.headers),
        config
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Apply request interceptors
   */
  private async applyRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = config;

    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onRequest) {
        try {
          processedConfig = await interceptor.onRequest(processedConfig);
        } catch (error) {
          if (interceptor.onRequestError) {
            await interceptor.onRequestError(error as Error);
          }
          throw error;
        }
      }
    }

    return processedConfig;
  }

  /**
   * Apply response interceptors
   */
  private async applyResponseInterceptors<T>(response: ApiResponse<T>): Promise<ApiResponse<T>> {
    let processedResponse = response;

    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onResponse) {
        try {
          processedResponse = await interceptor.onResponse(processedResponse);
        } catch (error) {
          if (interceptor.onResponseError) {
            await interceptor.onResponseError(error as ServiceError);
          }
          throw error;
        }
      }
    }

    return processedResponse;
  }

  /**
   * Merge configuration with defaults
   */
  private mergeConfig(config: RequestConfig): RequestConfig {
    return {
      ...config,
      headers: {
        ...this.defaultHeaders,
        ...config.headers
      },
      timeout: config.timeout || this.timeout
    };
  }

  /**
   * Build full URL
   */
  private buildUrl(path: string): string {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }
    
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${this.baseURL}${cleanPath}`;
  }

  /**
   * Parse response data
   */
  private async parseResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    if (contentType?.includes('application/json')) {
      return await response.json();
    }

    return await response.text() as unknown as T;
  }

  /**
   * Parse response headers
   */
  private parseHeaders(headers: Headers): Record<string, string> {
    const headerObj: Record<string, string> = {};
    headers.forEach((value, key) => {
      headerObj[key] = value;
    });
    return headerObj;
  }

  /**
   * Transform error to ServiceError
   */
  private async transformError(error: unknown, _config: RequestConfig): Promise<ServiceError> {
    if (error instanceof Error) {
      const serviceError: ServiceError = {
        name: 'ServiceError',
        message: error.message,
        code: 'UNKNOWN_ERROR',
        recoverable: false,
        originalError: error
      };

      // Handle specific error types
      if (error.name === 'AbortError') {
        serviceError.code = 'REQUEST_ABORTED';
        serviceError.userMessage = 'Request was cancelled';
        serviceError.recoverable = true;
      } else if (error.message.includes('fetch')) {
        serviceError.code = 'NETWORK_ERROR';
        serviceError.userMessage = 'Network connection failed';
        serviceError.recoverable = true;
      } else if (error.message.includes('timeout')) {
        serviceError.code = 'TIMEOUT_ERROR';
        serviceError.userMessage = 'Request timed out';
        serviceError.recoverable = true;
      }

      return serviceError;
    }

    return {
      name: 'ServiceError',
      message: 'Unknown error occurred',
      code: 'UNKNOWN_ERROR',
      recoverable: false
    };
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: ServiceError): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'SERVER_ERROR'
    ];

    return retryableCodes.includes(error.code) || 
           (error.status !== undefined && error.status >= 500);
  }
}
