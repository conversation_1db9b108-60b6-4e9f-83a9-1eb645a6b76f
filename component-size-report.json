{"timestamp": "2025-07-06T06:22:40.219Z", "summary": {"totalFiles": 262, "compliantFiles": 210, "violatingFiles": 52, "worstViolation": {"file": "src\\pages\\locations\\surrey-hills\\index.tsx", "lines": 823, "type": "feature", "limit": 250, "violation": true, "excess": 573}}, "violations": [{"file": "src\\pages\\locations\\surrey-hills\\index.tsx", "lines": 823, "type": "feature", "limit": 250, "violation": true, "excess": 573}, {"file": "src\\pages\\patient-resources\\LifestyleModifications.tsx", "lines": 805, "type": "feature", "limit": 250, "violation": true, "excess": 555}, {"file": "src\\pages\\locations\\frankston\\index.tsx", "lines": 789, "type": "feature", "limit": 250, "violation": true, "excess": 539}, {"file": "src\\pages\\patient-resources\\SpineConditionsLibrary.tsx", "lines": 730, "type": "feature", "limit": 250, "violation": true, "excess": 480}, {"file": "src\\pages\\Expertise.tsx", "lines": 698, "type": "feature", "limit": 250, "violation": true, "excess": 448}, {"file": "src\\pages\\locations\\heidelberg\\index.tsx", "lines": 661, "type": "feature", "limit": 250, "violation": true, "excess": 411}, {"file": "src\\pages\\locations\\sunbury\\index.tsx", "lines": 659, "type": "feature", "limit": 250, "violation": true, "excess": 409}, {"file": "src\\pages\\locations\\werribee\\index.tsx", "lines": 659, "type": "feature", "limit": 250, "violation": true, "excess": 409}, {"file": "src\\pages\\locations\\moonee-ponds\\index.tsx", "lines": 647, "type": "feature", "limit": 250, "violation": true, "excess": 397}, {"file": "src\\pages\\locations\\mornington\\index.tsx", "lines": 642, "type": "feature", "limit": 250, "violation": true, "excess": 392}, {"file": "src\\pages\\Index.tsx", "lines": 641, "type": "feature", "limit": 250, "violation": true, "excess": 391}, {"file": "src\\pages\\locations\\dandenong\\index.tsx", "lines": 582, "type": "feature", "limit": 250, "violation": true, "excess": 332}, {"file": "src\\pages\\Medicolegal.tsx", "lines": 573, "type": "feature", "limit": 250, "violation": true, "excess": 323}, {"file": "src\\pages\\locations\\langwarrin\\index.tsx", "lines": 560, "type": "feature", "limit": 250, "violation": true, "excess": 310}, {"file": "src\\pages\\locations\\bundoora\\index.tsx", "lines": 559, "type": "feature", "limit": 250, "violation": true, "excess": 309}, {"file": "src\\pages\\locations\\wantirna\\index.tsx", "lines": 548, "type": "feature", "limit": 250, "violation": true, "excess": 298}, {"file": "src\\pages\\patient-resources\\CervicalSpineExercises.tsx", "lines": 541, "type": "feature", "limit": 250, "violation": true, "excess": 291}, {"file": "src\\pages\\patient-resources\\SpineSafeExercises.tsx", "lines": 538, "type": "feature", "limit": 250, "violation": true, "excess": 288}, {"file": "src\\pages\\patient-resources\\ExerciseLibrary.tsx", "lines": 518, "type": "feature", "limit": 250, "violation": true, "excess": 268}, {"file": "src\\pages\\patient-resources\\YouthfulSpine.tsx", "lines": 498, "type": "feature", "limit": 250, "violation": true, "excess": 248}, {"file": "src\\pages\\patient-resources\\PatientDashboard.tsx", "lines": 482, "type": "feature", "limit": 250, "violation": true, "excess": 232}, {"file": "src\\pages\\patient-resources\\SpineAnatomy.tsx", "lines": 481, "type": "feature", "limit": 250, "violation": true, "excess": 231}, {"file": "src\\pages\\gp-resources\\Diagnostics.tsx", "lines": 454, "type": "feature", "limit": 250, "violation": true, "excess": 204}, {"file": "src\\pages\\expertise\\RoboticSpineSurgery.tsx", "lines": 436, "type": "feature", "limit": 250, "violation": true, "excess": 186}, {"file": "src\\pages\\patient-resources\\AgeSpecificSpineRecommendations.tsx", "lines": 431, "type": "feature", "limit": 250, "violation": true, "excess": 181}, {"file": "src\\pages\\gp-resources\\CareCoordination.tsx", "lines": 422, "type": "feature", "limit": 250, "violation": true, "excess": 172}, {"file": "src\\pages\\patient-resources\\CervicalSpineInjury.tsx", "lines": 417, "type": "feature", "limit": 250, "violation": true, "excess": 167}, {"file": "src\\pages\\patient-resources\\ExercisePainMedRisks.tsx", "lines": 409, "type": "feature", "limit": 250, "violation": true, "excess": 159}, {"file": "src\\pages\\expertise\\ImageGuidedSurgery.tsx", "lines": 377, "type": "feature", "limit": 250, "violation": true, "excess": 127}, {"file": "src\\pages\\locations\\LocationDetail.tsx", "lines": 371, "type": "feature", "limit": 250, "violation": true, "excess": 121}, {"file": "src\\pages\\patient-resources\\AssessmentTools.tsx", "lines": 356, "type": "feature", "limit": 250, "violation": true, "excess": 106}, {"file": "src\\pages\\patient-resources\\SpineHealthApp.tsx", "lines": 346, "type": "feature", "limit": 250, "violation": true, "excess": 96}, {"file": "src\\components\\EmptyState.tsx", "lines": 337, "type": "feature", "limit": 250, "violation": true, "excess": 87}, {"file": "src\\pages\\gp-resources\\Emergencies.tsx", "lines": 334, "type": "feature", "limit": 250, "violation": true, "excess": 84}, {"file": "src\\pages\\patient-resources\\conditions\\FacetArthropathyRefactored.tsx", "lines": 311, "type": "feature", "limit": 250, "violation": true, "excess": 61}, {"file": "src\\pages\\expertise\\lumbar-disc-replacement\\RisksComparison.tsx", "lines": 306, "type": "feature", "limit": 250, "violation": true, "excess": 56}, {"file": "src\\pages\\Locations.tsx", "lines": 304, "type": "feature", "limit": 250, "violation": true, "excess": 54}, {"file": "src\\pages\\patient-resources\\IndividualSpineHealthProgramme.tsx", "lines": 301, "type": "feature", "limit": 250, "violation": true, "excess": 51}, {"file": "src\\components\\Loading.tsx", "lines": 300, "type": "feature", "limit": 250, "violation": true, "excess": 50}, {"file": "src\\pages\\gp-resources\\ReferralProtocols.tsx", "lines": 299, "type": "feature", "limit": 250, "violation": true, "excess": 49}, {"file": "src\\components\\AsyncContent.tsx", "lines": 294, "type": "feature", "limit": 250, "violation": true, "excess": 44}, {"file": "src\\pages\\patient-resources\\SpineAndBrainHealth.tsx", "lines": 291, "type": "feature", "limit": 250, "violation": true, "excess": 41}, {"file": "src\\pages\\Specialties.tsx", "lines": 289, "type": "feature", "limit": 250, "violation": true, "excess": 39}, {"file": "src\\components\\contact\\InteractiveMapsSection.tsx", "lines": 269, "type": "feature", "limit": 250, "violation": true, "excess": 19}, {"file": "src\\pages\\expertise\\cervical-disc-replacement\\RisksComparison.tsx", "lines": 265, "type": "feature", "limit": 250, "violation": true, "excess": 15}, {"file": "src\\components\\IndependentReviewsSection.tsx", "lines": 265, "type": "feature", "limit": 250, "violation": true, "excess": 15}, {"file": "src\\components\\contact\\HospitalAffiliationsSection.tsx", "lines": 264, "type": "feature", "limit": 250, "violation": true, "excess": 14}, {"file": "src\\pages\\expertise\\lumbar-disc-replacement\\SurgeryRecovery.tsx", "lines": 263, "type": "feature", "limit": 250, "violation": true, "excess": 13}, {"file": "src\\hooks\\useContentState.ts", "lines": 259, "type": "feature", "limit": 250, "violation": true, "excess": 9}, {"file": "src\\pages\\Gallery.tsx", "lines": 257, "type": "feature", "limit": 250, "violation": true, "excess": 7}, {"file": "src\\components\\medical-conditions\\facet-arthropathy\\FacetJointAnatomySection.tsx", "lines": 255, "type": "feature", "limit": 250, "violation": true, "excess": 5}, {"file": "src\\components\\medical-conditions\\arthrosis\\ArthrosisTypesSection.tsx", "lines": 251, "type": "feature", "limit": 250, "violation": true, "excess": 1}], "limits": {"page": 300, "feature": 250, "ui": 200, "utility": 150}}