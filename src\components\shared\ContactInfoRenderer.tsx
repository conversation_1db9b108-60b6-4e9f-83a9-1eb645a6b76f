import React from 'react';
import { ExternalLink } from 'lucide-react';

import { cn } from '@/lib/utils';
import {
  getContactSectionStyles,
  getContactTitleStyles,
  getContactContentStyles,
  type ContactInfoType
} from '@/lib/style-utilities';

// ContactInfoType is now imported from style-utilities

export interface ContactItem {
  label: string;
  value: string;
  isLink?: boolean;
  isPhone?: boolean;
  isEmail?: boolean;
}

export interface ContactSection {
  type: ContactInfoType;
  title: string;
  items?: ContactItem[];
  content?: string;
  className?: string;
}

/**
 * Props for the contact info renderer
 */
export interface ContactInfoRendererProps {
  sections: ContactSection[];
  title?: string;
  className?: string;
  showTitle?: boolean;
}

// Style functions are now imported from style-utilities

/**
 * Render a contact item with appropriate styling and links
 */
export const ContactItemRenderer: React.FC<{
  item: ContactItem;
  sectionType: ContactInfoType;
}> = ({ item, sectionType }) => {
  const renderValue = () => {
    if (item.isLink) {
      return (
        <a 
          href={item.value} 
          target="_blank" 
          rel="noopener noreferrer" 
          className="underline hover:text-blue-600 ml-1 inline-flex items-center gap-1"
        >
          {item.value}
          <ExternalLink className="h-3 w-3" />
        </a>
      );
    }

    if (item.isPhone) {
      return (
        <a 
          href={`tel:${item.value.replace(/\s/g, '')}`}
          className="underline hover:text-blue-600 ml-1"
        >
          {item.value}
        </a>
      );
    }

    if (item.isEmail) {
      return (
        <a 
          href={`mailto:${item.value}`}
          className="underline hover:text-blue-600 ml-1"
        >
          {item.value}
        </a>
      );
    }

    return (
      <span className={
        sectionType === 'emergency' 
          ? "text-red-700 dark:text-red-400 font-bold ml-2"
          : "ml-1"
      }>
        {item.value}
      </span>
    );
  };

  return (
    <p className="text-sm">
      <span className="font-bold">{item.label}</span>
      {renderValue()}
    </p>
  );
};

/**
 * Shared contact information renderer component
 * Consolidates duplicate contact section rendering logic
 */
export const ContactInfoRenderer: React.FC<ContactInfoRendererProps> = ({
  sections,
  title,
  className = "",
  showTitle = true
}) => {
  return (
    <div className={className}>
      {showTitle && title && (
        <h3 className="text-xl font-bold mb-4">{title}</h3>
      )}
      <div className="glass-card p-6 mb-8">
        <div className="space-y-4">
          {sections.map((section, index) => (
            <div 
              key={index} 
              className={cn(
                getContactSectionStyles(section.type),
                section.className
              )}
            >
              <h4 className={getContactTitleStyles(section.type)}>
                {section.title}
              </h4>
              
              {section.items && (
                <div className={getContactContentStyles(section.type)}>
                  {section.items.map((item, itemIndex) => (
                    <ContactItemRenderer
                      key={itemIndex}
                      item={item}
                      sectionType={section.type}
                    />
                  ))}
                </div>
              )}
              
              {section.content && (
                <p className={getContactTitleStyles(section.type)}>
                  <strong>Important:</strong> {section.content}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactInfoRenderer;
