import React from 'react';
import { ExternalLink } from 'lucide-react';

import { cn } from '@/lib/utils';

/**
 * Contact Information Types
 */
export type ContactInfoType = 'emergency' | 'hospital' | 'warning' | 'info' | 'primary';

export interface ContactItem {
  label: string;
  value: string;
  isLink?: boolean;
  isPhone?: boolean;
  isEmail?: boolean;
}

export interface ContactSection {
  type: ContactInfoType;
  title: string;
  items?: ContactItem[];
  content?: string;
  className?: string;
}

/**
 * Props for the contact info renderer
 */
export interface ContactInfoRendererProps {
  sections: ContactSection[];
  title?: string;
  className?: string;
  showTitle?: boolean;
}

/**
 * Get styling classes for different contact section types
 */
export function getContactSectionStyles(type: ContactInfoType): string {
  switch (type) {
    case 'emergency':
      return "p-4 border border-red-300 bg-red-50 dark:bg-red-950/20 rounded-md";
    case 'hospital':
      return "p-4 border border-blue-300 bg-blue-50 dark:bg-blue-950/20 rounded-md";
    case 'warning':
      return "p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-md border border-yellow-200";
    case 'info':
      return "p-4 border border-gray-300 bg-gray-50 dark:bg-gray-950/20 rounded-md";
    case 'primary':
      return "p-4 border border-primary/30 bg-primary/5 rounded-md";
    default:
      return "p-4 border rounded-md";
  }
}

/**
 * Get title styling classes for different contact section types
 */
export function getContactTitleStyles(type: ContactInfoType): string {
  switch (type) {
    case 'emergency':
      return "font-bold text-red-900 dark:text-red-100 mb-2";
    case 'hospital':
      return "font-bold text-blue-900 dark:text-blue-100 mb-2";
    case 'warning':
      return "font-bold text-yellow-900 dark:text-yellow-100 mb-2";
    case 'info':
      return "font-bold text-gray-900 dark:text-gray-100 mb-2";
    case 'primary':
      return "font-bold text-primary mb-2";
    default:
      return "font-bold mb-2";
  }
}

/**
 * Get content styling classes for different contact section types
 */
export function getContactContentStyles(type: ContactInfoType): string {
  switch (type) {
    case 'emergency':
      return "space-y-1 text-red-800 dark:text-red-200";
    case 'hospital':
      return "space-y-1 text-blue-800 dark:text-blue-200";
    case 'warning':
      return "space-y-1 text-yellow-800 dark:text-yellow-200";
    case 'info':
      return "space-y-1 text-gray-800 dark:text-gray-200";
    case 'primary':
      return "space-y-1 text-primary";
    default:
      return "space-y-1";
  }
}

/**
 * Render a contact item with appropriate styling and links
 */
export const ContactItemRenderer: React.FC<{
  item: ContactItem;
  sectionType: ContactInfoType;
}> = ({ item, sectionType }) => {
  const renderValue = () => {
    if (item.isLink) {
      return (
        <a 
          href={item.value} 
          target="_blank" 
          rel="noopener noreferrer" 
          className="underline hover:text-blue-600 ml-1 inline-flex items-center gap-1"
        >
          {item.value}
          <ExternalLink className="h-3 w-3" />
        </a>
      );
    }

    if (item.isPhone) {
      return (
        <a 
          href={`tel:${item.value.replace(/\s/g, '')}`}
          className="underline hover:text-blue-600 ml-1"
        >
          {item.value}
        </a>
      );
    }

    if (item.isEmail) {
      return (
        <a 
          href={`mailto:${item.value}`}
          className="underline hover:text-blue-600 ml-1"
        >
          {item.value}
        </a>
      );
    }

    return (
      <span className={
        sectionType === 'emergency' 
          ? "text-red-700 dark:text-red-400 font-bold ml-2"
          : "ml-1"
      }>
        {item.value}
      </span>
    );
  };

  return (
    <p className="text-sm">
      <span className="font-bold">{item.label}</span>
      {renderValue()}
    </p>
  );
};

/**
 * Shared contact information renderer component
 * Consolidates duplicate contact section rendering logic
 */
export const ContactInfoRenderer: React.FC<ContactInfoRendererProps> = ({
  sections,
  title,
  className = "",
  showTitle = true
}) => {
  return (
    <div className={className}>
      {showTitle && title && (
        <h3 className="text-xl font-bold mb-4">{title}</h3>
      )}
      <div className="glass-card p-6 mb-8">
        <div className="space-y-4">
          {sections.map((section, index) => (
            <div 
              key={index} 
              className={cn(
                getContactSectionStyles(section.type),
                section.className
              )}
            >
              <h4 className={getContactTitleStyles(section.type)}>
                {section.title}
              </h4>
              
              {section.items && (
                <div className={getContactContentStyles(section.type)}>
                  {section.items.map((item, itemIndex) => (
                    <ContactItemRenderer
                      key={itemIndex}
                      item={item}
                      sectionType={section.type}
                    />
                  ))}
                </div>
              )}
              
              {section.content && (
                <p className={getContactTitleStyles(section.type)}>
                  <strong>Important:</strong> {section.content}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactInfoRenderer;
