import React from 'react';

import Footer from '@/components/Footer';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationCTA
} from '@/components/locations';
import Navbar from '@/components/Navbar';
import { useLocationData } from '@/hooks/useLocationData';

const SurreyHillsLocationRefactored: React.FC = () => {
  const locationData = useLocationData('surreyHillsLocation');

  // Surrey Hills specific data
  const surreyHillsData = {
    ...locationData,
    contact: {
      ...locationData.contact,
      address: {
        street: 'Suite 4, 619 Canterbury Road',
        suburb: 'Surrey Hills',
        state: 'VIC',
        postcode: '3127'
      },
      phone: '03 90084200',
      email: '<EMAIL>',
      hours: {
        weekdays: 'Monday to Friday: 8:30 AM - 5:30 PM',
        note: 'Consultations by appointment only'
      }
    },
    map: {
      ...locationData.map,
      embedUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.8351288553624!2d145.09308731531866!3d-37.***********!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad646b5d2ba4273%3A0x4045675218ccd90!2s619-625%20Canterbury%20Rd%2C%20Surrey%20Hills%20VIC%203127%2C%20Australia!5e0!3m2!1sen!2sus!4v1650000000000!5m2!1sen!2sus',
      transportOptions: {
        publicTransport: 'Our Surrey Hills location is a short walk from Surrey Hills train station on the Lilydale/Belgrave line. Several bus routes also service Canterbury Road, making it easily accessible from various parts of Melbourne.',
        car: 'Ample on-street parking is available on Canterbury Road and surrounding streets. The building is easily accessible from the Eastern Freeway and Burwood Highway, with convenient access from most parts of Melbourne.'
      }
    },
    facilities: {
      title: 'Our Facilities',
      subtitle: 'Modern, comfortable facilities designed for healthcare professionals and patient care',
      description: 'At miNEURO Medical Consulting Suites, we are committed to providing healthcare professionals with the resources and support tailored to meet diverse needs. Our facility offers for rent sessional or permanent fully-equipped state-of-the-art medical consulting suites in Surrey Hills at a prime location near key medical institutions, a bustling shopping district, and a convenient transportation hub.',
      items: [
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          ),
          title: 'Modern Consultation Suites',
          description: 'Our consultation suites are meticulously designed and equipped to support various medical specialties. Each suite has modern medical equipment, including examination tables, sinks, ergonomic seating, and ample storage space. The suites feature large displays to review and discuss imaging, helping patients understand their conditions and treatment options.'
        },
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          ),
          title: 'Advanced Medical Equipment',
          description: 'State-of-the-art medical equipment and technology to support comprehensive patient care and accurate diagnostics.'
        },
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          ),
          title: 'Professional Support',
          description: 'Dedicated administrative and clinical support staff to ensure smooth operations and excellent patient experience.'
        }
      ]
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <div className="flex-1 pt-20">
        <LocationHero
          title={surreyHillsData.hero.title}
          subtitle={surreyHillsData.hero.subtitle}
          introduction1={surreyHillsData.hero.introduction1}
          introduction2={surreyHillsData.hero.introduction2}
          introduction3={surreyHillsData.hero.introduction3}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={surreyHillsData.contact.address}
                phone={surreyHillsData.contact.phone}
                email={surreyHillsData.contact.email}
                hours={surreyHillsData.contact.hours}
                consultingHours={{
                  title: 'Consulting Hours',
                  details: 'Monday to Friday: 8:30 AM - 5:30 PM',
                  appointmentNote: 'Consultations are by appointment only. Please call our office to schedule an appointment.',
                  urgentNote: 'Urgent appointments are available on request. Our staff will do their best to accommodate patients with urgent conditions as quickly as possible.'
                }}
                appointmentProcess={{
                  title: 'Appointment Process',
                  details1: 'Before your appointment, our office will liaise with your GP to obtain a referral and relevant medical information, including results of previous imaging and other investigations.',
                  details2: 'All new patients will be asked to fill out a detailed registration form to help us understand the nature and urgency of your problem. This information helps Dr. Aliashkevich prepare for your consultation and provide the most appropriate care.'
                }}
              />

              <LocationMap
                embedUrl={surreyHillsData.map.embedUrl}
                title="Surrey Hills"
                transportOptions={surreyHillsData.map.transportOptions}
              />
            </div>
          </div>
        </div>

        {surreyHillsData.facilities && (
          <LocationFacilities
            title={surreyHillsData.facilities.title}
            subtitle={surreyHillsData.facilities.subtitle}
            description={surreyHillsData.facilities.description}
            facilities={surreyHillsData.facilities.items}
          />
        )}

        <LocationCTA
          title={surreyHillsData.cta.title}
          description={surreyHillsData.cta.description}
          buttons={surreyHillsData.cta.buttons}
        />
      </div>

      <Footer />
    </div>
  );
};

SurreyHillsLocationRefactored.displayName = 'SurreyHillsLocationRefactored';

export default SurreyHillsLocationRefactored;
