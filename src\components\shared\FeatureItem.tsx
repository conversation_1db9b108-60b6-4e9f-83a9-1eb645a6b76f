import React from 'react';

import { FeatureItem as SharedFeatureItem, type BaseItemProps } from './ItemRenderer';
import { type AlignmentVariant, type SizeVariant, type ColorVariant } from '@/lib/style-utilities';

interface FeatureItemProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  link?: string;
  layout?: 'vertical' | 'horizontal';
  alignment?: AlignmentVariant;
  iconVariant?: ColorVariant;
  iconSize?: SizeVariant;
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
}

const FeatureItem: React.FC<FeatureItemProps> = (props) => {
  return <SharedFeatureItem {...props} />;
};

export default FeatureItem;
