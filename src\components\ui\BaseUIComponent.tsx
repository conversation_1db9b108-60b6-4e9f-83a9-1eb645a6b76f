import React from 'react';

import ErrorBoundary from '@/components/ErrorBoundary';
import { cn } from '@/lib/utils';

/**
 * Base UI component patterns for consistent implementation
 */

/**
 * Create a context with error handling
 */
export function createUIContext<T>(contextName: string) {
  const Context = React.createContext<T | null>(null);

  function useContext() {
    const context = React.useContext(Context);
    if (!context) {
      throw new Error(`use${contextName} must be used within a <${contextName} />`);
    }
    return context;
  }

  function Provider({ 
    children, 
    value 
  }: { 
    children: React.ReactNode; 
    value: T;
  }) {
    return (
      <ErrorBoundary>
        <Context.Provider value={value}>
          {children}
        </Context.Provider>
      </ErrorBoundary>
    );
  }

  return {
    Context,
    Provider,
    useContext
  };
}

/**
 * Base forwardRef component wrapper with error boundary
 */
export function createForwardRefComponent<
  TElement extends HTMLElement,
  TProps extends React.HTMLAttributes<TElement>
>(
  displayName: string,
  render: (
    props: TProps,
    ref: React.ForwardedRef<TElement>
  ) => React.ReactElement | null
) {
  const Component = React.forwardRef<TElement, TProps>((props, ref) => {
    return (
      <ErrorBoundary>
        {render(props, ref)}
      </ErrorBoundary>
    );
  });

  Component.displayName = displayName;
  return Component;
}

/**
 * Common UI patterns
 */
export const uiPatterns = {
  /**
   * Generate unique ID pattern
   */
  useUniqueId: (prefix: string, id?: string) => {
    const uniqueId = React.useId();
    return `${prefix}-${id || uniqueId.replace(/:/g, "")}`;
  },

  /**
   * Merge class names pattern
   */
  mergeClassNames: (...classes: (string | undefined)[]) => {
    return cn(...classes);
  },

  /**
   * Create controlled/uncontrolled state pattern
   */
  useControlledState: <T>(
    value: T | undefined,
    defaultValue: T,
    onChange?: (value: T) => void
  ) => {
    const [internalValue, setInternalValue] = React.useState(defaultValue);
    const isControlled = value !== undefined;
    const currentValue = isControlled ? value : internalValue;

    const setValue = React.useCallback((newValue: T) => {
      if (!isControlled) {
        setInternalValue(newValue);
      }
      onChange?.(newValue);
    }, [isControlled, onChange]);

    return [currentValue, setValue] as const;
  },

  /**
   * Create ref callback pattern
   */
  useRefCallback: <T>(
    callback: (node: T | null) => void,
    deps: React.DependencyList
  ) => {
    return React.useCallback(callback, deps);
  },

  /**
   * Create event handler pattern
   */
  createEventHandler: <T extends Event>(
    handler: (event: T) => void,
    preventDefault = false,
    stopPropagation = false
  ) => {
    return (event: T) => {
      if (preventDefault) {
        event.preventDefault();
      }
      if (stopPropagation) {
        event.stopPropagation();
      }
      handler(event);
    };
  },

  /**
   * Create keyboard navigation pattern
   */
  useKeyboardNavigation: (
    handlers: Record<string, () => void>,
    enabled = true
  ) => {
    return React.useCallback((event: React.KeyboardEvent) => {
      if (!enabled) return;
      
      const handler = handlers[event.key];
      if (handler) {
        event.preventDefault();
        handler();
      }
    }, [handlers, enabled]);
  },

  /**
   * Create focus management pattern
   */
  useFocusManagement: (
    autoFocus = false,
    restoreFocus = true
  ) => {
    const previousActiveElement = React.useRef<Element | null>(null);
    const elementRef = React.useRef<HTMLElement | null>(null);

    React.useEffect(() => {
      if (autoFocus && elementRef.current) {
        previousActiveElement.current = document.activeElement;
        elementRef.current.focus();
      }

      return () => {
        if (restoreFocus && previousActiveElement.current instanceof HTMLElement) {
          previousActiveElement.current.focus();
        }
      };
    }, [autoFocus, restoreFocus]);

    return elementRef;
  }
};

/**
 * Common prop types
 */
export interface BaseUIProps {
  className?: string;
  children?: React.ReactNode;
}

export interface BaseInteractiveProps extends BaseUIProps {
  disabled?: boolean;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

export default createUIContext;
