import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { arthrosisData } from '@/data/conditions/arthrosis';

/**
 * Arthrosis Component
 * 
 * Comprehensive guide for arthrosis (spondylosis) using modular,
 * data-driven architecture for maintainability
 */

const Arthrosis: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Arthrosis (Spondylosis) - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={arthrosisData.hero.title}
          subtitle={arthrosisData.hero.subtitle}
          backgroundImage={arthrosisData.hero.backgroundImage}
          badge={arthrosisData.hero.badge}
        />

        <ConditionQuickFacts facts={arthrosisData.quickFacts} />

        <ConditionOverviewSection
          title={arthrosisData.overview.title}
          description={arthrosisData.overview.description}
          keyPoints={arthrosisData.overview.keyPoints}
          imageSrc={arthrosisData.overview.imageSrc}
          imageAlt={arthrosisData.overview.imageAlt}
          imageCaption={arthrosisData.overview.imageCaption}
        />

        <ConditionCauses
          causes={arthrosisData.causes}
          riskFactors={arthrosisData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={arthrosisData.symptoms}
          warningSigns={arthrosisData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={arthrosisData.conservativeTreatments}
          surgicalOptions={arthrosisData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

Arthrosis.displayName = 'Arthrosis';

export default Arthrosis;
