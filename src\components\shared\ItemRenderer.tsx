import React from 'react';
import { Link } from 'react-router-dom';

import IconContainer from './IconContainer';

import { cn } from '@/lib/utils';
import { getAlignmentClass, type AlignmentVariant, type SizeVariant, type ColorVariant } from '@/lib/style-utilities';

/**
 * Base item interface for shared properties
 */
export interface BaseItemProps {
  title: string;
  content?: string | React.ReactNode;
  description?: string;
  icon?: React.ReactNode;
  link?: string;
  layout?: 'horizontal' | 'vertical';
  alignment?: AlignmentVariant;
  iconVariant?: ColorVariant;
  iconSize?: SizeVariant;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
  descriptionClassName?: string;
}

/**
 * Get layout classes for item arrangement
 */
export function getItemLayoutClass(layout: 'horizontal' | 'vertical'): string {
  switch (layout) {
    case 'horizontal':
      return 'flex items-start space-x-4';
    case 'vertical':
    default:
      return 'flex flex-col items-center text-center space-y-3';
  }
}

/**
 * Unified item renderer component
 * Consolidates duplicate item rendering patterns across ContactInfoItem and FeatureItem
 */
export const ItemRenderer: React.FC<BaseItemProps> = ({
  title,
  content,
  description,
  icon,
  link,
  layout = 'vertical',
  alignment = 'center',
  iconVariant = 'primary',
  iconSize = 'md',
  className = '',
  titleClassName = '',
  contentClassName = '',
  descriptionClassName = ''
}) => {
  const layoutClass = getItemLayoutClass(layout);
  const alignmentClass = getAlignmentClass(alignment);

  const renderContent = () => {
    const contentElement = (
      <div className={cn(
        layout === 'horizontal' ? 'flex-1' : 'w-full',
        alignmentClass
      )}>
        <h3 className={cn(
          'font-semibold mb-2',
          layout === 'horizontal' ? 'text-lg' : 'text-xl',
          titleClassName
        )}>
          {title}
        </h3>
        
        {content && (
          <div className={cn(
            'text-muted-foreground',
            layout === 'horizontal' ? 'text-sm' : 'text-base',
            contentClassName
          )}>
            {content}
          </div>
        )}
        
        {description && (
          <p className={cn(
            'text-muted-foreground',
            layout === 'horizontal' ? 'text-sm' : 'text-base',
            descriptionClassName
          )}>
            {description}
          </p>
        )}
      </div>
    );

    if (link) {
      return (
        <Link 
          to={link} 
          className="block hover:opacity-80 transition-opacity"
        >
          {contentElement}
        </Link>
      );
    }

    return contentElement;
  };

  return (
    <div className={cn(layoutClass, className)}>
      {icon && (
        <IconContainer
          icon={icon}
          variant={iconVariant}
          size={iconSize}
          className={layout === 'horizontal' ? 'flex-shrink-0' : ''}
        />
      )}
      
      {renderContent()}
    </div>
  );
};

/**
 * Contact info item variant
 */
export const ContactInfoItem: React.FC<BaseItemProps> = (props) => (
  <ItemRenderer
    {...props}
    layout={props.layout || 'horizontal'}
    alignment={props.alignment || 'left'}
  />
);

/**
 * Feature item variant
 */
export const FeatureItem: React.FC<BaseItemProps> = (props) => (
  <ItemRenderer
    {...props}
    layout={props.layout || 'vertical'}
    alignment={props.alignment || 'center'}
  />
);

export default ItemRenderer;
